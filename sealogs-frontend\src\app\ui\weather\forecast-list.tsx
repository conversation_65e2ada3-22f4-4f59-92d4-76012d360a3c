'use client'
import { ReadWeatherForecasts } from '@/app/lib/graphQL/query'
import { UpdateWeatherForecast } from '@/app/lib/graphQL/mutation'
import WeatherForecastModel from '@/app/offline/models/weatherForecast'
import { Skeleton } from '@/components/ui/skeleton'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { useLazyQuery, useMutation } from '@apollo/client'
import dayjs from 'dayjs'
import { useEffect, useState, useCallback } from 'react'
import WindWidget from './widgets/wind-widget'
import CloudWidget from './widgets/cloud-widget'
import SwellWidget from './widgets/swell-widget'
import { MessageSquare, MessageSquareText, Loader2 } from 'lucide-react'
import TableWrapper from '@/components/ui/table-wrapper'
import { cn } from '@/app/lib/utils'
import { Tooltip<PERSON>ontent, TooltipTrigger, Tooltip } from '@/components/ui'

const WeatherForecastList = ({
    logBookEntryID,
    refreshList = false,
    onClick,
    offline = false,
}: {
    logBookEntryID: number
    refreshList?: boolean
    onClick?: any
    offline?: boolean
}) => {
    const [isLoading, setIsLoading] = useState(true)
    const [forecasts, setForecasts] = useState<any[]>([])
    const [openCommentDialog, setOpenCommentDialog] = useState(false)
    const [currentComment, setCurrentComment] = useState('')
    const [currentForecast, setCurrentForecast] = useState<any>(null)
    const [isSaving, setIsSaving] = useState(false)
    const [hasError, setHasError] = useState(false)

    const forecastModel = new WeatherForecastModel()

    // Sort forecasts by date and time (latest forecast date/time first)
    const sortForecastsByDateTime = useCallback((forecasts: any[]) => {
        return [...forecasts].sort((a, b) => {
            const dateTimeA = dayjs(`${a.day} ${a.time}`)
            const dateTimeB = dayjs(`${b.day} ${b.time}`)
            return dateTimeB.valueOf() - dateTimeA.valueOf() // Descending order (latest first)
        })
    }, [])

    const [readWeatherForecasts, { loading: readWeatherForecastsLoading }] =
        useLazyQuery(ReadWeatherForecasts, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response) => {
                const forecasts = response?.readWeatherForecasts?.nodes
                const sortedForecasts = sortForecastsByDateTime(forecasts || [])
                setForecasts(sortedForecasts)
            },
            onError: (error) => {
                console.error('ReadWeatherForecasts error', error)
            },
        })

    const [updateWeatherForecast] = useMutation(UpdateWeatherForecast, {
        onCompleted: () => {
            setOpenCommentDialog(false)
            setIsSaving(false)
            setHasError(false)
            loadForecasts() // Refresh the list
        },
        onError: (error) => {
            console.error('UpdateWeatherForecast error', error)
            setHasError(true)
            setIsSaving(false)
        },
    })
    const loadForecasts = useCallback(async () => {
        if (offline) {
            const forecasts =
                await forecastModel.getByLogBookEntryID(logBookEntryID)
            const sortedForecasts = sortForecastsByDateTime(forecasts || [])
            setForecasts(sortedForecasts)
        } else {
            await readWeatherForecasts({
                variables: {
                    filter: {
                        logBookEntryID: { eq: logBookEntryID },
                    },
                },
            })
        }
    }, [
        offline,
        logBookEntryID,
        forecastModel,
        readWeatherForecasts,
        sortForecastsByDateTime,
    ])

    const handleOpenCommentDialog = (forecast: any) => {
        setCurrentForecast(forecast)
        setCurrentComment(forecast.comment || '')
        setOpenCommentDialog(true)
        setHasError(false)
    }

    const handleSaveComment = async () => {
        if (!currentForecast) return

        setIsSaving(true)
        setHasError(false)

        try {
            if (offline) {
                // Update using offline model
                const updatedForecast = {
                    ...currentForecast,
                    comment: currentComment,
                }
                // Remove GraphQL typename if present
                if (updatedForecast.__typename)
                    delete updatedForecast.__typename
                if (updatedForecast.geoLocation)
                    delete updatedForecast.geoLocation

                await forecastModel.save(updatedForecast)
                setOpenCommentDialog(false)
                setIsSaving(false)
                loadForecasts() // Refresh the list
            } else {
                // Update using GraphQL mutation
                const input = {
                    id: currentForecast.id,
                    comment: currentComment,
                }

                await updateWeatherForecast({
                    variables: { input },
                })
            }
        } catch (error) {
            console.error('Error saving comment:', error)
            setHasError(true)
            setIsSaving(false)
        }
    }

    const handleDeleteComment = async () => {
        if (!currentForecast) return

        setIsSaving(true)
        setHasError(false)

        try {
            if (offline) {
                // Update using offline model
                const updatedForecast = {
                    ...currentForecast,
                    comment: '',
                }
                // Remove GraphQL typename if present
                if (updatedForecast.__typename)
                    delete updatedForecast.__typename
                if (updatedForecast.geoLocation)
                    delete updatedForecast.geoLocation

                await forecastModel.save(updatedForecast)
                setOpenCommentDialog(false)
                setIsSaving(false)
                loadForecasts() // Refresh the list
            } else {
                // Update using GraphQL mutation
                const input = {
                    id: currentForecast.id,
                    comment: '',
                }

                await updateWeatherForecast({
                    variables: { input },
                })
            }
        } catch (error) {
            console.error('Error deleting comment:', error)
            setHasError(true)
            setIsSaving(false)
        }
    }

    useEffect(() => {
        if (isLoading || refreshList) {
            loadForecasts()
            setIsLoading(false)
        }
    }, [isLoading, refreshList, loadForecasts])
    return (
        <div>
            {readWeatherForecastsLoading && <WeatherForecastListSkeleton />}
            {!readWeatherForecastsLoading && (
                <div>
                    {forecasts.length > 0 ? (
                        <div>
                            <TableWrapper
                                headings={[
                                    'Forecast',
                                    'Wind',
                                    'Cloud',
                                    'Swell',
                                    'Comment',
                                ]}>
                                {forecasts.map((forecast: any) => (
                                    <tr
                                        key={forecast.id}
                                        className={`group border-b  hover: `}>
                                        <td className="px-6 py-4 text-left">
                                            <div
                                                className="cursor-pointer"
                                                role="button"
                                                tabIndex={0}
                                                onClick={() => {
                                                    onClick(forecast)
                                                }}
                                                onKeyDown={(e) => {
                                                    if (
                                                        e.key === 'Enter' ||
                                                        e.key === ' '
                                                    ) {
                                                        e.preventDefault()
                                                        onClick(forecast)
                                                    }
                                                }}>
                                                <div className="text-left">
                                                    <div className="text-2xl font-light">
                                                        {dayjs(
                                                            `${forecast.day} ${forecast.time}`,
                                                        ).format(
                                                            'DD MMMM, HH:mm',
                                                        )}
                                                    </div>
                                                    <div className="uppercase text-curious-blue-400">
                                                        {+forecast.geoLocationID >
                                                        0
                                                            ? forecast
                                                                  .geoLocation
                                                                  .title
                                                            : ''}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <WindWidget
                                                direction={
                                                    forecast.windDirection
                                                }
                                                speed={forecast.windSpeed}
                                                editMode={false}
                                                iconOnly
                                            />
                                        </td>
                                        <td>
                                            <CloudWidget
                                                visibilityValue={
                                                    forecast.visibility
                                                }
                                                precipitationValue={
                                                    forecast.precipitation
                                                }
                                                cloudCoverValue={
                                                    forecast.cloudCover
                                                }
                                                editMode={false}
                                                iconOnly
                                            />
                                        </td>
                                        <td>
                                            <SwellWidget
                                                value={forecast.swell}
                                                editMode={false}
                                                iconOnly
                                            />
                                        </td>
                                        <td>
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        iconOnly
                                                        title={
                                                            forecast.comment
                                                                ? 'Edit comment'
                                                                : 'Add comment'
                                                        }
                                                        className="group"
                                                        iconLeft={
                                                            forecast.comment ? (
                                                                <MessageSquareText
                                                                    className={cn(
                                                                        'text-curious-blue-400',
                                                                    )}
                                                                    size={24}
                                                                />
                                                            ) : (
                                                                <MessageSquare
                                                                    className={cn(
                                                                        'text-neutral-400 group-hover:text-neutral-400/50',
                                                                        'will-change-transform will-change-width will-change-padding transform-gpu',
                                                                        'group-hover:transition-colors group-hover:ease-out group-hover:duration-300',
                                                                    )}
                                                                    size={24}
                                                                />
                                                            )
                                                        }
                                                        onClick={() =>
                                                            handleOpenCommentDialog(
                                                                forecast,
                                                            )
                                                        }
                                                    />
                                                </TooltipTrigger>
                                                {forecast.comment && (
                                                    <TooltipContent align="end">
                                                        {forecast.comment}
                                                    </TooltipContent>
                                                )}
                                            </Tooltip>
                                        </td>
                                    </tr>
                                ))}
                            </TableWrapper>
                        </div>
                    ) : (
                        <div></div>
                    )}
                </div>
            )}

            {/* Comment Dialog */}
            <AlertDialogNew
                openDialog={openCommentDialog}
                setOpenDialog={setOpenCommentDialog}
                title={currentComment ? 'Edit comment' : 'Add comment'}
                handleCreate={handleSaveComment}
                handleDestructiveAction={
                    currentComment ? handleDeleteComment : undefined
                }
                showDestructiveAction={!!currentComment}
                actionText={isSaving ? 'Saving...' : 'Save'}
                destructiveActionText="Delete"
                destructiveLoading={isSaving}
                cancelText="Cancel"
                size="lg"
                loading={isSaving}>
                <div className="flex flex-col">
                    {hasError && (
                        <div className="text-destructive mb-2 text-sm">
                            Error {currentComment ? 'updating' : 'saving'}{' '}
                            comment. Please try again.
                        </div>
                    )}
                    <Textarea
                        id="forecast-comment"
                        disabled={isSaving}
                        rows={4}
                        placeholder="Comment"
                        value={currentComment}
                        onChange={(e) => setCurrentComment(e.target.value)}
                        className={cn('max-h-[60svh]', {
                            'border-destructive': hasError,
                        })}
                    />
                    {isSaving && (
                        <div className="flex items-center justify-center mt-2">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span className="text-sm">Saving...</span>
                        </div>
                    )}
                </div>
            </AlertDialogNew>
        </div>
    )
}

const WeatherForecastListSkeleton = () => {
    const numRows = 3 // number of rows to render

    return (
        <TableWrapper headings={['Time:firstHead', 'Location']}>
            {Array.from({ length: numRows }).map((_, index) => (
                <tr key={index} className={`group border-b  hover: `}>
                    <td className="px-6 py-4 text-left">
                        <Skeleton />
                    </td>
                    <td className="px-6 py-4">
                        <Skeleton />
                    </td>
                </tr>
            ))}
        </TableWrapper>
    )
}

export default WeatherForecastList
