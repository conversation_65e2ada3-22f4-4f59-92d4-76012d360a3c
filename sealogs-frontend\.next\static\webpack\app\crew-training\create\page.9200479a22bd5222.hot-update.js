"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/create/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx":
/*!***********************************************************************!*\
  !*** ./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx ***!
  \***********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _add_crew_member_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../add-crew-member-dialog */ \"(app-pages-browser)/./src/app/ui/crew/add-crew-member-dialog.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst CrewMultiSelectDropdown = (param)=>{\n    let { value = [], onChange, memberIdOptions = [], departments = [], filterByAdmin = false, offline = false, vesselID = 0 } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [crewList, setCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openCreateMemberDialog, setOpenCreateMemberDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIDs, setSelectedIDs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__[\"default\"]();\n    const handleSetCrewList = (crewListRaw)=>{\n        // If vesselID > 0, filter the crew list to display only the vessel crew.\n        const vesselCrewList = vesselID > 0 ? crewListRaw.filter((crew)=>crew.vehicles.nodes.some((vehicle)=>+vehicle.id === vesselID)) : crewListRaw;\n        const createOption = {\n            value: \"newCrewMember\",\n            label: \"--- Create Crew Member ---\"\n        };\n        const data = vesselCrewList.filter((crew)=>filterByAdmin ? !crewIsAdmin(crew) : true);\n        if (departments.length > 0) {\n            const departmentList = departments.flatMap((department)=>{\n                return department.id;\n            });\n            const crews = data.filter((crew)=>crew.departments.nodes.some((node)=>departmentList.includes(node.id))).map((item)=>{\n                var _item_firstName, _item_surname;\n                return {\n                    value: item.id,\n                    label: \"\".concat((_item_firstName = item.firstName) !== null && _item_firstName !== void 0 ? _item_firstName : \"\", \" \").concat((_item_surname = item.surname) !== null && _item_surname !== void 0 ? _item_surname : \"\")\n                };\n            });\n            if (memberIdOptions.length === 0) {\n                setCrewList([\n                    createOption,\n                    ...crews\n                ]);\n            } else {\n                const filteredCrewList = crews.filter((crew)=>{\n                    return memberIdOptions.includes(crew.value);\n                });\n                setCrewList(filteredCrewList);\n            }\n        } else {\n            const crews = data.map((item)=>{\n                var _item_firstName, _item_surname;\n                return {\n                    value: item.id,\n                    label: \"\".concat((_item_firstName = item.firstName) !== null && _item_firstName !== void 0 ? _item_firstName : \"\", \" \").concat((_item_surname = item.surname) !== null && _item_surname !== void 0 ? _item_surname : \"\")\n                };\n            });\n            if (memberIdOptions.length === 0) {\n                setCrewList([\n                    createOption,\n                    ...crews\n                ]);\n            } else {\n                const filteredCrewList = crews.filter((crew)=>{\n                    return memberIdOptions.includes(crew.value);\n                });\n                setCrewList(filteredCrewList);\n            }\n        }\n    };\n    const [querySeaLogsMembersList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_9__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_8__.ReadSeaLogsMembers, {\n        fetchPolicy: \"cache-and-network\",\n        onError: (error)=>{\n            console.error(\"querySeaLogsMembersList error\", error);\n        }\n    });\n    const loadCrewMembers = async ()=>{\n        let allMembers = [];\n        let offset = 0;\n        const limit = 100;\n        let hasNextPage = true;\n        try {\n            while(hasNextPage){\n                var _response_data;\n                const response = await querySeaLogsMembersList({\n                    variables: {\n                        filter: {\n                            isArchived: {\n                                eq: false\n                            }\n                        },\n                        limit: limit,\n                        offset: offset\n                    }\n                });\n                if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.readSeaLogsMembers) {\n                    const data = response.data.readSeaLogsMembers.nodes;\n                    const pageInfo = response.data.readSeaLogsMembers.pageInfo;\n                    if (data && data.length > 0) {\n                        allMembers = [\n                            ...allMembers,\n                            ...data\n                        ];\n                    }\n                    hasNextPage = (pageInfo === null || pageInfo === void 0 ? void 0 : pageInfo.hasNextPage) || false;\n                    offset += limit;\n                } else {\n                    hasNextPage = false;\n                }\n            }\n            // Set all collected members at once\n            if (allMembers.length > 0) {\n                handleSetCrewList(allMembers);\n            }\n        } catch (error) {\n            console.error(\"Error loading all crew members:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && !offline) {\n            loadCrewMembers();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading,\n        offline\n    ]);\n    // if (!offline) {\n    // getSeaLogsMembersList(handleSetCrewList)\n    // }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            seaLogsMemberModel.getAll().then((data)=>{\n                handleSetCrewList(data);\n            });\n        }\n    }, [\n        offline\n    ]);\n    const crewIsAdmin = (crew)=>{\n        var _crew_groups_nodes;\n        return ((_crew_groups_nodes = crew.groups.nodes) === null || _crew_groups_nodes === void 0 ? void 0 : _crew_groups_nodes.filter((permission)=>{\n            return permission.code === \"admin\";\n        }).length) > 0;\n    };\n    const handleOnChange = (value)=>{\n        if (!value) {\n            setSelectedIDs([]);\n            onChange([]);\n            return;\n        }\n        const valueArray = Array.isArray(value) ? value : [\n            value\n        ];\n        // Fix the condition to properly check for 'newCrewMember'\n        if (valueArray.find((option)=>option.value === \"newCrewMember\")) {\n            setOpenCreateMemberDialog(true);\n            return;\n        }\n        if (valueArray.length === 0) {\n            setSelectedIDs([]);\n            onChange([]);\n            return;\n        }\n        // Ensure we're working with valid Option objects\n        const validOptions = valueArray.filter((option)=>option && typeof option === \"object\");\n        setSelectedIDs(validOptions);\n        onChange(validOptions);\n    };\n    const [queryAddMember] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_10__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CREATE_USER, {\n        fetchPolicy: \"no-cache\",\n        onCompleted: (response)=>{\n            const data = response.createSeaLogsMember;\n            if (data.id > 0) {\n                setOpenCreateMemberDialog(false);\n                const newData = {\n                    value: data.id,\n                    label: data.firstName + \" \" + data.surname\n                };\n                setCrewList([\n                    ...crewList,\n                    newData\n                ]);\n                setSelectedIDs([\n                    ...selectedIDs,\n                    data.id\n                ]);\n                onChange([\n                    newData,\n                    ...value.map((id)=>{\n                        const crew = crewList.find((c)=>c.value === id);\n                        return crew;\n                    })\n                ]);\n                setError(false);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createUser error\", error.message);\n            setError(error);\n        }\n    });\n    const handleAddNewMember = async ()=>{\n        const variables = {\n            input: {\n                firstName: document.getElementById(\"crew-firstName\").value ? document.getElementById(\"crew-firstName\").value : null,\n                surname: document.getElementById(\"crew-surname\").value ? document.getElementById(\"crew-surname\").value : null,\n                email: document.getElementById(\"crew-email\").value ? document.getElementById(\"crew-email\").value : null,\n                phoneNumber: document.getElementById(\"crew-phoneNumber\").value ? document.getElementById(\"crew-phoneNumber\").value : null,\n                username: document.getElementById(\"crew-username\").value ? document.getElementById(\"crew-username\").value : null,\n                password: document.getElementById(\"crew-password\").value ? document.getElementById(\"crew-password\").value : null\n            }\n        };\n        if (offline) {\n            // queryAddMember\n            const data = await seaLogsMemberModel.save({\n                ...variables.input,\n                id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_5__.generateUniqueId)()\n            });\n            setOpenCreateMemberDialog(false);\n            const newData = {\n                value: data.id,\n                label: data.firstName + \" \" + data.surname\n            };\n            setCrewList([\n                ...crewList,\n                newData\n            ]);\n            setSelectedIDs([\n                ...selectedIDs,\n                data.id\n            ]);\n            onChange([\n                newData,\n                ...value.map((id)=>{\n                    const crew = crewList.find((c)=>c.value === id);\n                    return crew;\n                })\n            ]);\n            setError(false);\n        } else {\n            await queryAddMember({\n                variables: variables\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"CrewMultiSelectDropdown - Syncing value:\", value, \"crewList length:\", crewList.length);\n        // Handle empty value - clear selection\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(value) || value.length === 0) {\n            console.log(\"CrewMultiSelectDropdown - Clearing selection\");\n            setSelectedIDs([]);\n            return;\n        }\n        // Wait for crewList to be populated\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(crewList)) {\n            console.log(\"CrewMultiSelectDropdown - Waiting for crewList to load\");\n            return;\n        }\n        // Convert value array to proper Option objects\n        const selectedOptions = value.map((id)=>{\n            // Handle both string and number IDs\n            const normalizedId = String(id);\n            const option = crewList.find((crew)=>String(crew.value) === normalizedId);\n            if (!option) {\n                console.warn(\"CrewMultiSelectDropdown - Could not find crew with ID:\", id);\n                return {\n                    value: normalizedId,\n                    label: \"Unknown (\".concat(id, \")\")\n                };\n            }\n            return option;\n        }).filter(Boolean) // Remove any null/undefined options\n        ;\n        console.log(\"CrewMultiSelectDropdown - Setting selected options:\", selectedOptions);\n        setSelectedIDs(selectedOptions);\n    }, [\n        value,\n        crewList\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_6__.Combobox, {\n                options: crewList,\n                value: selectedIDs,\n                onChange: handleOnChange,\n                placeholder: \"Select Crew\",\n                multi: true,\n                isLoading: !crewList\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\multiselect-dropdown\\\\multiselect-dropdown.tsx\",\n                lineNumber: 369,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_add_crew_member_dialog__WEBPACK_IMPORTED_MODULE_7__.AddCrewMemberDialog, {\n                openDialog: openCreateMemberDialog,\n                setOpenDialog: setOpenCreateMemberDialog,\n                handleCreate: handleAddNewMember,\n                actionText: \"Add Crew Member\",\n                error: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\multiselect-dropdown\\\\multiselect-dropdown.tsx\",\n                lineNumber: 377,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CrewMultiSelectDropdown, \"B5kTBW1ijKbUdQ1QBKs5KkaV5Fg=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_9__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_10__.useMutation\n    ];\n});\n_c = CrewMultiSelectDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewMultiSelectDropdown);\nvar _c;\n$RefreshReg$(_c, \"CrewMultiSelectDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvY3Jldy9tdWx0aXNlbGVjdC1kcm9wZG93bi9tdWx0aXNlbGVjdC1kcm9wZG93bi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUMyQztBQUNYO0FBQ3dCO0FBQ0U7QUFFUztBQUNEO0FBQ1A7QUFDSTtBQUNqQjtBQUU5QyxNQUFNVywwQkFBMEI7UUFBQyxFQUM3QkMsUUFBUSxFQUFFLEVBQ1ZDLFFBQVEsRUFDUkMsa0JBQWtCLEVBQUUsRUFDcEJDLGNBQWMsRUFBRSxFQUNoQkMsZ0JBQWdCLEtBQUssRUFDckJDLFVBQVUsS0FBSyxFQUNmQyxXQUFXLENBQUMsRUFTZjs7SUFDRyxNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR25CLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ29CLFVBQVVDLFlBQVksR0FBR3JCLCtDQUFRQSxDQUFDLEVBQUU7SUFDM0MsTUFBTSxDQUFDc0Isd0JBQXdCQywwQkFBMEIsR0FBR3ZCLCtDQUFRQSxDQUFDO0lBQ3JFLE1BQU0sQ0FBQ3dCLGFBQWFDLGVBQWUsR0FBR3pCLCtDQUFRQSxDQUFDLEVBQUU7SUFDakQsTUFBTSxDQUFDMEIsT0FBT0MsU0FBUyxHQUFHM0IsK0NBQVFBLENBQU07SUFDeEMsTUFBTTRCLHFCQUFxQixJQUFJdkIseUVBQWtCQTtJQUNqRCxNQUFNd0Isb0JBQW9CLENBQUNDO1FBQ3ZCLHlFQUF5RTtRQUN6RSxNQUFNQyxpQkFDRmQsV0FBVyxJQUNMYSxZQUFZRSxNQUFNLENBQUMsQ0FBQ0MsT0FDaEJBLEtBQUtDLFFBQVEsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQ3BCLENBQUNDLFVBQWlCLENBQUNBLFFBQVFDLEVBQUUsS0FBS3JCLGFBRzFDYTtRQUNWLE1BQU1TLGVBQWU7WUFDakI1QixPQUFPO1lBQ1A2QixPQUFPO1FBQ1g7UUFDQSxNQUFNQyxPQUFPVixlQUFlQyxNQUFNLENBQUMsQ0FBQ0MsT0FDaENsQixnQkFBZ0IsQ0FBQzJCLFlBQVlULFFBQVE7UUFFekMsSUFBSW5CLFlBQVk2QixNQUFNLEdBQUcsR0FBRztZQUN4QixNQUFNQyxpQkFBaUI5QixZQUFZK0IsT0FBTyxDQUFDLENBQUNDO2dCQUN4QyxPQUFPQSxXQUFXUixFQUFFO1lBQ3hCO1lBQ0EsTUFBTVMsUUFBUU4sS0FDVFQsTUFBTSxDQUFDLENBQUNDLE9BQ0xBLEtBQUtuQixXQUFXLENBQUNxQixLQUFLLENBQUNDLElBQUksQ0FBQyxDQUFDWSxPQUN6QkosZUFBZUssUUFBUSxDQUFDRCxLQUFLVixFQUFFLElBR3RDWSxHQUFHLENBQUMsQ0FBQ0M7b0JBR1lBLGlCQUF3QkE7Z0JBRnRDLE9BQU87b0JBQ0h4QyxPQUFPd0MsS0FBS2IsRUFBRTtvQkFDZEUsT0FBTyxHQUEyQlcsT0FBeEJBLENBQUFBLGtCQUFBQSxLQUFLQyxTQUFTLGNBQWRELDZCQUFBQSxrQkFBa0IsSUFBRyxLQUFzQixPQUFuQkEsQ0FBQUEsZ0JBQUFBLEtBQUtFLE9BQU8sY0FBWkYsMkJBQUFBLGdCQUFnQjtnQkFDdEQ7WUFDSjtZQUNKLElBQUl0QyxnQkFBZ0I4QixNQUFNLEtBQUssR0FBRztnQkFDOUJ0QixZQUFZO29CQUFDa0I7dUJBQWlCUTtpQkFBTTtZQUN4QyxPQUFPO2dCQUNILE1BQU1PLG1CQUFtQlAsTUFBTWYsTUFBTSxDQUFDLENBQUNDO29CQUNuQyxPQUFPcEIsZ0JBQWdCb0MsUUFBUSxDQUFDaEIsS0FBS3RCLEtBQUs7Z0JBQzlDO2dCQUNBVSxZQUFZaUM7WUFDaEI7UUFDSixPQUFPO1lBQ0gsTUFBTVAsUUFBUU4sS0FBS1MsR0FBRyxDQUFDLENBQUNDO29CQUdOQSxpQkFBd0JBO2dCQUZ0QyxPQUFPO29CQUNIeEMsT0FBT3dDLEtBQUtiLEVBQUU7b0JBQ2RFLE9BQU8sR0FBMkJXLE9BQXhCQSxDQUFBQSxrQkFBQUEsS0FBS0MsU0FBUyxjQUFkRCw2QkFBQUEsa0JBQWtCLElBQUcsS0FBc0IsT0FBbkJBLENBQUFBLGdCQUFBQSxLQUFLRSxPQUFPLGNBQVpGLDJCQUFBQSxnQkFBZ0I7Z0JBQ3REO1lBQ0o7WUFDQSxJQUFJdEMsZ0JBQWdCOEIsTUFBTSxLQUFLLEdBQUc7Z0JBQzlCdEIsWUFBWTtvQkFBQ2tCO3VCQUFpQlE7aUJBQU07WUFDeEMsT0FBTztnQkFDSCxNQUFNTyxtQkFBbUJQLE1BQU1mLE1BQU0sQ0FBQyxDQUFDQztvQkFDbkMsT0FBT3BCLGdCQUFnQm9DLFFBQVEsQ0FBQ2hCLEtBQUt0QixLQUFLO2dCQUM5QztnQkFDQVUsWUFBWWlDO1lBQ2hCO1FBQ0o7SUFDSjtJQUNBLE1BQU0sQ0FBQ0Msd0JBQXdCLEdBQUdwRCw0REFBWUEsQ0FBQ00sd0RBQWtCQSxFQUFFO1FBQy9EK0MsYUFBYTtRQUNiQyxTQUFTLENBQUMvQjtZQUNOZ0MsUUFBUWhDLEtBQUssQ0FBQyxpQ0FBaUNBO1FBQ25EO0lBQ0o7SUFDQSxNQUFNaUMsa0JBQWtCO1FBQ3BCLElBQUlDLGFBQW9CLEVBQUU7UUFDMUIsSUFBSUMsU0FBUztRQUNiLE1BQU1DLFFBQVE7UUFDZCxJQUFJQyxjQUFjO1FBRWxCLElBQUk7WUFDQSxNQUFPQSxZQUFhO29CQVNaQztnQkFSSixNQUFNQSxXQUFXLE1BQU1ULHdCQUF3QjtvQkFDM0NVLFdBQVc7d0JBQ1BqQyxRQUFROzRCQUFFa0MsWUFBWTtnQ0FBRUMsSUFBSTs0QkFBTTt3QkFBRTt3QkFDcENMLE9BQU9BO3dCQUNQRCxRQUFRQTtvQkFDWjtnQkFDSjtnQkFFQSxLQUFJRyxpQkFBQUEsU0FBU3ZCLElBQUksY0FBYnVCLHFDQUFBQSxlQUFlSSxrQkFBa0IsRUFBRTtvQkFDbkMsTUFBTTNCLE9BQU91QixTQUFTdkIsSUFBSSxDQUFDMkIsa0JBQWtCLENBQUNqQyxLQUFLO29CQUNuRCxNQUFNa0MsV0FBV0wsU0FBU3ZCLElBQUksQ0FBQzJCLGtCQUFrQixDQUFDQyxRQUFRO29CQUUxRCxJQUFJNUIsUUFBUUEsS0FBS0UsTUFBTSxHQUFHLEdBQUc7d0JBQ3pCaUIsYUFBYTsrQkFBSUE7K0JBQWVuQjt5QkFBSztvQkFDekM7b0JBRUFzQixjQUFjTSxDQUFBQSxxQkFBQUEsK0JBQUFBLFNBQVVOLFdBQVcsS0FBSTtvQkFDdkNGLFVBQVVDO2dCQUNkLE9BQU87b0JBQ0hDLGNBQWM7Z0JBQ2xCO1lBQ0o7WUFFQSxvQ0FBb0M7WUFDcEMsSUFBSUgsV0FBV2pCLE1BQU0sR0FBRyxHQUFHO2dCQUN2QmQsa0JBQWtCK0I7WUFDdEI7UUFDSixFQUFFLE9BQU9sQyxPQUFPO1lBQ1pnQyxRQUFRaEMsS0FBSyxDQUFDLG1DQUFtQ0E7UUFDckQ7SUFDSjtJQUNBM0IsZ0RBQVNBLENBQUM7UUFDTixJQUFJbUIsYUFBYSxDQUFDRixTQUFTO1lBQ3ZCMkM7WUFDQXhDLGFBQWE7UUFDakI7SUFDSixHQUFHO1FBQUNEO1FBQVdGO0tBQVE7SUFDdkIsa0JBQWtCO0lBQ2xCLDJDQUEyQztJQUMzQyxJQUFJO0lBRUpqQixnREFBU0EsQ0FBQztRQUNOLElBQUlpQixTQUFTO1lBQ1RZLG1CQUFtQjBDLE1BQU0sR0FBR0MsSUFBSSxDQUFDLENBQUM5QjtnQkFDOUJaLGtCQUFrQlk7WUFDdEI7UUFDSjtJQUNKLEdBQUc7UUFBQ3pCO0tBQVE7SUFFWixNQUFNMEIsY0FBYyxDQUFDVDtZQUViQTtRQURKLE9BQ0lBLEVBQUFBLHFCQUFBQSxLQUFLdUMsTUFBTSxDQUFDckMsS0FBSyxjQUFqQkYseUNBQUFBLG1CQUFtQkQsTUFBTSxDQUFDLENBQUN5QztZQUN2QixPQUFPQSxXQUFXQyxJQUFJLEtBQUs7UUFDL0IsR0FBRy9CLE1BQU0sSUFBRztJQUVwQjtJQUVBLE1BQU1nQyxpQkFBaUIsQ0FBQ2hFO1FBQ3BCLElBQUksQ0FBQ0EsT0FBTztZQUNSYyxlQUFlLEVBQUU7WUFDakJiLFNBQVMsRUFBRTtZQUNYO1FBQ0o7UUFFQSxNQUFNZ0UsYUFBYUMsTUFBTUMsT0FBTyxDQUFDbkUsU0FBU0EsUUFBUTtZQUFDQTtTQUFNO1FBRXpELDBEQUEwRDtRQUMxRCxJQUNJaUUsV0FBV0csSUFBSSxDQUFDLENBQUNDLFNBQWdCQSxPQUFPckUsS0FBSyxLQUFLLGtCQUNwRDtZQUNFWSwwQkFBMEI7WUFDMUI7UUFDSjtRQUVBLElBQUlxRCxXQUFXakMsTUFBTSxLQUFLLEdBQUc7WUFDekJsQixlQUFlLEVBQUU7WUFDakJiLFNBQVMsRUFBRTtZQUNYO1FBQ0o7UUFFQSxpREFBaUQ7UUFDakQsTUFBTXFFLGVBQWVMLFdBQVc1QyxNQUFNLENBQ2xDLENBQUNnRCxTQUFXQSxVQUFVLE9BQU9BLFdBQVc7UUFHNUN2RCxlQUFld0Q7UUFDZnJFLFNBQVNxRTtJQUNiO0lBRUEsTUFBTSxDQUFDQyxlQUFlLEdBQUc5RSw0REFBV0EsQ0FBQ0Ysa0VBQVdBLEVBQUU7UUFDOUNzRCxhQUFhO1FBQ2IyQixhQUFhLENBQUNuQjtZQUNWLE1BQU12QixPQUFPdUIsU0FBU29CLG1CQUFtQjtZQUN6QyxJQUFJM0MsS0FBS0gsRUFBRSxHQUFHLEdBQUc7Z0JBQ2JmLDBCQUEwQjtnQkFDMUIsTUFBTThELFVBQVU7b0JBQ1oxRSxPQUFPOEIsS0FBS0gsRUFBRTtvQkFDZEUsT0FBT0MsS0FBS1csU0FBUyxHQUFHLE1BQU1YLEtBQUtZLE9BQU87Z0JBQzlDO2dCQUNBaEMsWUFBWTt1QkFBSUQ7b0JBQVVpRTtpQkFBUTtnQkFDbEM1RCxlQUFlO3VCQUFJRDtvQkFBYWlCLEtBQUtILEVBQUU7aUJBQUM7Z0JBQ3hDMUIsU0FBUztvQkFDTHlFO3VCQUNHMUUsTUFBTXVDLEdBQUcsQ0FBQyxDQUFDWjt3QkFDVixNQUFNTCxPQUFPYixTQUFTMkQsSUFBSSxDQUFDLENBQUNPLElBQVdBLEVBQUUzRSxLQUFLLEtBQUsyQjt3QkFDbkQsT0FBT0w7b0JBQ1g7aUJBQ0g7Z0JBQ0ROLFNBQVM7WUFDYjtRQUNKO1FBQ0E4QixTQUFTLENBQUMvQjtZQUNOZ0MsUUFBUWhDLEtBQUssQ0FBQyxvQkFBb0JBLE1BQU02RCxPQUFPO1lBQy9DNUQsU0FBU0Q7UUFDYjtJQUNKO0lBRUEsTUFBTThELHFCQUFxQjtRQUN2QixNQUFNdkIsWUFBWTtZQUNkd0IsT0FBTztnQkFDSHJDLFdBQVcsU0FDRXVDLGNBQWMsQ0FDbkIsa0JBRU5oRixLQUFLLEdBQ0QsU0FDYWdGLGNBQWMsQ0FDbkIsa0JBRU5oRixLQUFLLEdBQ1A7Z0JBQ04wQyxTQUFTLFNBQ0lzQyxjQUFjLENBQUMsZ0JBQzFCaEYsS0FBSyxHQUNELFNBQ2FnRixjQUFjLENBQ25CLGdCQUVOaEYsS0FBSyxHQUNQO2dCQUNOaUYsT0FBTyxTQUNNRCxjQUFjLENBQUMsY0FDMUJoRixLQUFLLEdBQ0QsU0FDYWdGLGNBQWMsQ0FDbkIsY0FFTmhGLEtBQUssR0FDUDtnQkFDTmtGLGFBQWEsU0FDQUYsY0FBYyxDQUNuQixvQkFFTmhGLEtBQUssR0FDRCxTQUNhZ0YsY0FBYyxDQUNuQixvQkFFTmhGLEtBQUssR0FDUDtnQkFDTm1GLFVBQVUsU0FDR0gsY0FBYyxDQUFDLGlCQUMxQmhGLEtBQUssR0FDRCxTQUNhZ0YsY0FBYyxDQUNuQixpQkFFTmhGLEtBQUssR0FDUDtnQkFDTm9GLFVBQVUsU0FDR0osY0FBYyxDQUFDLGlCQUMxQmhGLEtBQUssR0FDRCxTQUNhZ0YsY0FBYyxDQUNuQixpQkFFTmhGLEtBQUssR0FDUDtZQUNWO1FBQ0o7UUFDQSxJQUFJSyxTQUFTO1lBQ1QsaUJBQWlCO1lBQ2pCLE1BQU15QixPQUFPLE1BQU1iLG1CQUFtQm9FLElBQUksQ0FBQztnQkFDdkMsR0FBRy9CLFVBQVV3QixLQUFLO2dCQUNsQm5ELElBQUloQyxnRkFBZ0JBO1lBQ3hCO1lBQ0FpQiwwQkFBMEI7WUFDMUIsTUFBTThELFVBQVU7Z0JBQ1oxRSxPQUFPOEIsS0FBS0gsRUFBRTtnQkFDZEUsT0FBT0MsS0FBS1csU0FBUyxHQUFHLE1BQU1YLEtBQUtZLE9BQU87WUFDOUM7WUFDQWhDLFlBQVk7bUJBQUlEO2dCQUFVaUU7YUFBUTtZQUNsQzVELGVBQWU7bUJBQUlEO2dCQUFhaUIsS0FBS0gsRUFBRTthQUFDO1lBQ3hDMUIsU0FBUztnQkFDTHlFO21CQUNHMUUsTUFBTXVDLEdBQUcsQ0FBQyxDQUFDWjtvQkFDVixNQUFNTCxPQUFPYixTQUFTMkQsSUFBSSxDQUFDLENBQUNPLElBQVdBLEVBQUUzRSxLQUFLLEtBQUsyQjtvQkFDbkQsT0FBT0w7Z0JBQ1g7YUFDSDtZQUNETixTQUFTO1FBQ2IsT0FBTztZQUNILE1BQU11RCxlQUFlO2dCQUNqQmpCLFdBQVdBO1lBQ2Y7UUFDSjtJQUNKO0lBRUFsRSxnREFBU0EsQ0FBQztRQUNOMkQsUUFBUXVDLEdBQUcsQ0FDUCw0Q0FDQXRGLE9BQ0Esb0JBQ0FTLFNBQVN1QixNQUFNO1FBR25CLHVDQUF1QztRQUN2QyxJQUFJMUMscURBQU9BLENBQUNVLFVBQVVBLE1BQU1nQyxNQUFNLEtBQUssR0FBRztZQUN0Q2UsUUFBUXVDLEdBQUcsQ0FBQztZQUNaeEUsZUFBZSxFQUFFO1lBQ2pCO1FBQ0o7UUFFQSxvQ0FBb0M7UUFDcEMsSUFBSXhCLHFEQUFPQSxDQUFDbUIsV0FBVztZQUNuQnNDLFFBQVF1QyxHQUFHLENBQ1A7WUFFSjtRQUNKO1FBRUEsK0NBQStDO1FBQy9DLE1BQU1DLGtCQUFrQnZGLE1BQ25CdUMsR0FBRyxDQUFDLENBQUNaO1lBQ0Ysb0NBQW9DO1lBQ3BDLE1BQU02RCxlQUFlQyxPQUFPOUQ7WUFDNUIsTUFBTTBDLFNBQVM1RCxTQUFTMkQsSUFBSSxDQUN4QixDQUFDOUMsT0FBY21FLE9BQU9uRSxLQUFLdEIsS0FBSyxNQUFNd0Y7WUFHMUMsSUFBSSxDQUFDbkIsUUFBUTtnQkFDVHRCLFFBQVEyQyxJQUFJLENBQ1IsMERBQ0EvRDtnQkFFSixPQUFPO29CQUFFM0IsT0FBT3dGO29CQUFjM0QsT0FBTyxZQUFlLE9BQUhGLElBQUc7Z0JBQUc7WUFDM0Q7WUFFQSxPQUFPMEM7UUFDWCxHQUNDaEQsTUFBTSxDQUFDc0UsU0FBUyxvQ0FBb0M7O1FBRXpENUMsUUFBUXVDLEdBQUcsQ0FDUCx1REFDQUM7UUFFSnpFLGVBQWV5RTtJQUNuQixHQUFHO1FBQUN2RjtRQUFPUztLQUFTO0lBRXBCLHFCQUNJOzswQkFDSSw4REFBQ2IsNkRBQVFBO2dCQUNMZ0csU0FBU25GO2dCQUNUVCxPQUFPYTtnQkFDUFosVUFBVStEO2dCQUNWNkIsYUFBWTtnQkFDWkMsS0FBSztnQkFDTHZGLFdBQVcsQ0FBQ0U7Ozs7OzswQkFFaEIsOERBQUNaLHdFQUFtQkE7Z0JBQ2hCa0csWUFBWXBGO2dCQUNacUYsZUFBZXBGO2dCQUNmcUYsY0FBY3BCO2dCQUNkcUIsWUFBVztnQkFDWG5GLE9BQU9BOzs7Ozs7OztBQUl2QjtHQXJYTWhCOztRQWlGZ0NQLHdEQUFZQTtRQXVHckJDLHdEQUFXQTs7O0tBeExsQ007QUF1WE4sK0RBQWVBLHVCQUF1QkEsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL3VpL2NyZXcvbXVsdGlzZWxlY3QtZHJvcGRvd24vbXVsdGlzZWxlY3QtZHJvcGRvd24udHN4PzdhZTgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcclxuaW1wb3J0IHsgaXNFbXB0eSB9IGZyb20gJ2xvZGFzaCdcclxuaW1wb3J0IHsgQ1JFQVRFX1VTRVIgfSBmcm9tICdAL2FwcC9saWIvZ3JhcGhRTC9tdXRhdGlvbidcclxuaW1wb3J0IHsgdXNlTGF6eVF1ZXJ5LCB1c2VNdXRhdGlvbiB9IGZyb20gJ0BhcG9sbG8vY2xpZW50J1xyXG5cclxuaW1wb3J0IFNlYUxvZ3NNZW1iZXJNb2RlbCBmcm9tICdAL2FwcC9vZmZsaW5lL21vZGVscy9zZWFMb2dzTWVtYmVyJ1xyXG5pbXBvcnQgeyBnZW5lcmF0ZVVuaXF1ZUlkIH0gZnJvbSAnQC9hcHAvb2ZmbGluZS9oZWxwZXJzL2Z1bmN0aW9ucydcclxuaW1wb3J0IHsgQ29tYm9ib3gsIE9wdGlvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jb21ib0JveCdcclxuaW1wb3J0IHsgQWRkQ3Jld01lbWJlckRpYWxvZyB9IGZyb20gJy4uL2FkZC1jcmV3LW1lbWJlci1kaWFsb2cnXHJcbmltcG9ydCB7IFJlYWRTZWFMb2dzTWVtYmVycyB9IGZyb20gJy4vcXVlcmllcydcclxuXHJcbmNvbnN0IENyZXdNdWx0aVNlbGVjdERyb3Bkb3duID0gKHtcclxuICAgIHZhbHVlID0gW10sIC8vIGFuIGFycmF5IG9mIGNyZXcgSURzXHJcbiAgICBvbkNoYW5nZSxcclxuICAgIG1lbWJlcklkT3B0aW9ucyA9IFtdLFxyXG4gICAgZGVwYXJ0bWVudHMgPSBbXSxcclxuICAgIGZpbHRlckJ5QWRtaW4gPSBmYWxzZSxcclxuICAgIG9mZmxpbmUgPSBmYWxzZSxcclxuICAgIHZlc3NlbElEID0gMCxcclxufToge1xyXG4gICAgdmFsdWU6IGFueVtdXHJcbiAgICBvbkNoYW5nZTogYW55XHJcbiAgICBtZW1iZXJJZE9wdGlvbnM/OiBhbnlbXVxyXG4gICAgZGVwYXJ0bWVudHM/OiBhbnlcclxuICAgIGZpbHRlckJ5QWRtaW4/OiBib29sZWFuXHJcbiAgICBvZmZsaW5lPzogYm9vbGVhblxyXG4gICAgdmVzc2VsSUQ/OiBudW1iZXJcclxufSkgPT4ge1xyXG4gICAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXHJcbiAgICBjb25zdCBbY3Jld0xpc3QsIHNldENyZXdMaXN0XSA9IHVzZVN0YXRlKFtdIGFzIGFueSlcclxuICAgIGNvbnN0IFtvcGVuQ3JlYXRlTWVtYmVyRGlhbG9nLCBzZXRPcGVuQ3JlYXRlTWVtYmVyRGlhbG9nXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gICAgY29uc3QgW3NlbGVjdGVkSURzLCBzZXRTZWxlY3RlZElEc10gPSB1c2VTdGF0ZShbXSBhcyBhbnkpXHJcbiAgICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPGFueT4oZmFsc2UpXHJcbiAgICBjb25zdCBzZWFMb2dzTWVtYmVyTW9kZWwgPSBuZXcgU2VhTG9nc01lbWJlck1vZGVsKClcclxuICAgIGNvbnN0IGhhbmRsZVNldENyZXdMaXN0ID0gKGNyZXdMaXN0UmF3OiBhbnkpID0+IHtcclxuICAgICAgICAvLyBJZiB2ZXNzZWxJRCA+IDAsIGZpbHRlciB0aGUgY3JldyBsaXN0IHRvIGRpc3BsYXkgb25seSB0aGUgdmVzc2VsIGNyZXcuXHJcbiAgICAgICAgY29uc3QgdmVzc2VsQ3Jld0xpc3QgPVxyXG4gICAgICAgICAgICB2ZXNzZWxJRCA+IDBcclxuICAgICAgICAgICAgICAgID8gY3Jld0xpc3RSYXcuZmlsdGVyKChjcmV3OiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICBjcmV3LnZlaGljbGVzLm5vZGVzLnNvbWUoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKHZlaGljbGU6IGFueSkgPT4gK3ZlaGljbGUuaWQgPT09IHZlc3NlbElELFxyXG4gICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgOiBjcmV3TGlzdFJhd1xyXG4gICAgICAgIGNvbnN0IGNyZWF0ZU9wdGlvbiA9IHtcclxuICAgICAgICAgICAgdmFsdWU6ICduZXdDcmV3TWVtYmVyJyxcclxuICAgICAgICAgICAgbGFiZWw6ICctLS0gQ3JlYXRlIENyZXcgTWVtYmVyIC0tLScsXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnN0IGRhdGEgPSB2ZXNzZWxDcmV3TGlzdC5maWx0ZXIoKGNyZXc6IGFueSkgPT5cclxuICAgICAgICAgICAgZmlsdGVyQnlBZG1pbiA/ICFjcmV3SXNBZG1pbihjcmV3KSA6IHRydWUsXHJcbiAgICAgICAgKVxyXG4gICAgICAgIGlmIChkZXBhcnRtZW50cy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGRlcGFydG1lbnRMaXN0ID0gZGVwYXJ0bWVudHMuZmxhdE1hcCgoZGVwYXJ0bWVudDogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gZGVwYXJ0bWVudC5pZFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICBjb25zdCBjcmV3cyA9IGRhdGFcclxuICAgICAgICAgICAgICAgIC5maWx0ZXIoKGNyZXc6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICBjcmV3LmRlcGFydG1lbnRzLm5vZGVzLnNvbWUoKG5vZGU6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgZGVwYXJ0bWVudExpc3QuaW5jbHVkZXMobm9kZS5pZCksXHJcbiAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIC5tYXAoKGl0ZW06IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBpdGVtLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogYCR7aXRlbS5maXJzdE5hbWUgPz8gJyd9ICR7aXRlbS5zdXJuYW1lID8/ICcnfWAsXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgaWYgKG1lbWJlcklkT3B0aW9ucy5sZW5ndGggPT09IDApIHtcclxuICAgICAgICAgICAgICAgIHNldENyZXdMaXN0KFtjcmVhdGVPcHRpb24sIC4uLmNyZXdzXSlcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGZpbHRlcmVkQ3Jld0xpc3QgPSBjcmV3cy5maWx0ZXIoKGNyZXc6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBtZW1iZXJJZE9wdGlvbnMuaW5jbHVkZXMoY3Jldy52YWx1ZSlcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICBzZXRDcmV3TGlzdChmaWx0ZXJlZENyZXdMaXN0KVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgY29uc3QgY3Jld3MgPSBkYXRhLm1hcCgoaXRlbTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiBpdGVtLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgIGxhYmVsOiBgJHtpdGVtLmZpcnN0TmFtZSA/PyAnJ30gJHtpdGVtLnN1cm5hbWUgPz8gJyd9YCxcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgaWYgKG1lbWJlcklkT3B0aW9ucy5sZW5ndGggPT09IDApIHtcclxuICAgICAgICAgICAgICAgIHNldENyZXdMaXN0KFtjcmVhdGVPcHRpb24sIC4uLmNyZXdzXSlcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGZpbHRlcmVkQ3Jld0xpc3QgPSBjcmV3cy5maWx0ZXIoKGNyZXc6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBtZW1iZXJJZE9wdGlvbnMuaW5jbHVkZXMoY3Jldy52YWx1ZSlcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICBzZXRDcmV3TGlzdChmaWx0ZXJlZENyZXdMaXN0KVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgY29uc3QgW3F1ZXJ5U2VhTG9nc01lbWJlcnNMaXN0XSA9IHVzZUxhenlRdWVyeShSZWFkU2VhTG9nc01lbWJlcnMsIHtcclxuICAgICAgICBmZXRjaFBvbGljeTogJ2NhY2hlLWFuZC1uZXR3b3JrJyxcclxuICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdxdWVyeVNlYUxvZ3NNZW1iZXJzTGlzdCBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgIH0sXHJcbiAgICB9KVxyXG4gICAgY29uc3QgbG9hZENyZXdNZW1iZXJzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgIGxldCBhbGxNZW1iZXJzOiBhbnlbXSA9IFtdXHJcbiAgICAgICAgbGV0IG9mZnNldCA9IDBcclxuICAgICAgICBjb25zdCBsaW1pdCA9IDEwMFxyXG4gICAgICAgIGxldCBoYXNOZXh0UGFnZSA9IHRydWVcclxuXHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgd2hpbGUgKGhhc05leHRQYWdlKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHF1ZXJ5U2VhTG9nc01lbWJlcnNMaXN0KHtcclxuICAgICAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZmlsdGVyOiB7IGlzQXJjaGl2ZWQ6IHsgZXE6IGZhbHNlIH0gfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbGltaXQ6IGxpbWl0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvZmZzZXQ6IG9mZnNldCxcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfSlcclxuXHJcbiAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YT8ucmVhZFNlYUxvZ3NNZW1iZXJzKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLmRhdGEucmVhZFNlYUxvZ3NNZW1iZXJzLm5vZGVzXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcGFnZUluZm8gPSByZXNwb25zZS5kYXRhLnJlYWRTZWFMb2dzTWVtYmVycy5wYWdlSW5mb1xyXG5cclxuICAgICAgICAgICAgICAgICAgICBpZiAoZGF0YSAmJiBkYXRhLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYWxsTWVtYmVycyA9IFsuLi5hbGxNZW1iZXJzLCAuLi5kYXRhXVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgaGFzTmV4dFBhZ2UgPSBwYWdlSW5mbz8uaGFzTmV4dFBhZ2UgfHwgZmFsc2VcclxuICAgICAgICAgICAgICAgICAgICBvZmZzZXQgKz0gbGltaXRcclxuICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgaGFzTmV4dFBhZ2UgPSBmYWxzZVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBTZXQgYWxsIGNvbGxlY3RlZCBtZW1iZXJzIGF0IG9uY2VcclxuICAgICAgICAgICAgaWYgKGFsbE1lbWJlcnMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICAgICAgaGFuZGxlU2V0Q3Jld0xpc3QoYWxsTWVtYmVycylcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgYWxsIGNyZXcgbWVtYmVyczonLCBlcnJvcilcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmIChpc0xvYWRpbmcgJiYgIW9mZmxpbmUpIHtcclxuICAgICAgICAgICAgbG9hZENyZXdNZW1iZXJzKClcclxuICAgICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgICAgIH1cclxuICAgIH0sIFtpc0xvYWRpbmcsIG9mZmxpbmVdKVxyXG4gICAgLy8gaWYgKCFvZmZsaW5lKSB7XHJcbiAgICAvLyBnZXRTZWFMb2dzTWVtYmVyc0xpc3QoaGFuZGxlU2V0Q3Jld0xpc3QpXHJcbiAgICAvLyB9XHJcblxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBpZiAob2ZmbGluZSkge1xyXG4gICAgICAgICAgICBzZWFMb2dzTWVtYmVyTW9kZWwuZ2V0QWxsKCkudGhlbigoZGF0YTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBoYW5kbGVTZXRDcmV3TGlzdChkYXRhKVxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgIH1cclxuICAgIH0sIFtvZmZsaW5lXSlcclxuXHJcbiAgICBjb25zdCBjcmV3SXNBZG1pbiA9IChjcmV3OiBhbnkpID0+IHtcclxuICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICBjcmV3Lmdyb3Vwcy5ub2Rlcz8uZmlsdGVyKChwZXJtaXNzaW9uOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBwZXJtaXNzaW9uLmNvZGUgPT09ICdhZG1pbidcclxuICAgICAgICAgICAgfSkubGVuZ3RoID4gMFxyXG4gICAgICAgIClcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBoYW5kbGVPbkNoYW5nZSA9ICh2YWx1ZTogT3B0aW9uIHwgT3B0aW9uW10gfCBudWxsKSA9PiB7XHJcbiAgICAgICAgaWYgKCF2YWx1ZSkge1xyXG4gICAgICAgICAgICBzZXRTZWxlY3RlZElEcyhbXSlcclxuICAgICAgICAgICAgb25DaGFuZ2UoW10pXHJcbiAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgdmFsdWVBcnJheSA9IEFycmF5LmlzQXJyYXkodmFsdWUpID8gdmFsdWUgOiBbdmFsdWVdXHJcblxyXG4gICAgICAgIC8vIEZpeCB0aGUgY29uZGl0aW9uIHRvIHByb3Blcmx5IGNoZWNrIGZvciAnbmV3Q3Jld01lbWJlcidcclxuICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgIHZhbHVlQXJyYXkuZmluZCgob3B0aW9uOiBhbnkpID0+IG9wdGlvbi52YWx1ZSA9PT0gJ25ld0NyZXdNZW1iZXInKVxyXG4gICAgICAgICkge1xyXG4gICAgICAgICAgICBzZXRPcGVuQ3JlYXRlTWVtYmVyRGlhbG9nKHRydWUpXHJcbiAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKHZhbHVlQXJyYXkubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgICAgIHNldFNlbGVjdGVkSURzKFtdKVxyXG4gICAgICAgICAgICBvbkNoYW5nZShbXSlcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBFbnN1cmUgd2UncmUgd29ya2luZyB3aXRoIHZhbGlkIE9wdGlvbiBvYmplY3RzXHJcbiAgICAgICAgY29uc3QgdmFsaWRPcHRpb25zID0gdmFsdWVBcnJheS5maWx0ZXIoXHJcbiAgICAgICAgICAgIChvcHRpb24pID0+IG9wdGlvbiAmJiB0eXBlb2Ygb3B0aW9uID09PSAnb2JqZWN0JyxcclxuICAgICAgICApXHJcblxyXG4gICAgICAgIHNldFNlbGVjdGVkSURzKHZhbGlkT3B0aW9ucylcclxuICAgICAgICBvbkNoYW5nZSh2YWxpZE9wdGlvbnMpXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgW3F1ZXJ5QWRkTWVtYmVyXSA9IHVzZU11dGF0aW9uKENSRUFURV9VU0VSLCB7XHJcbiAgICAgICAgZmV0Y2hQb2xpY3k6ICduby1jYWNoZScsXHJcbiAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5jcmVhdGVTZWFMb2dzTWVtYmVyXHJcbiAgICAgICAgICAgIGlmIChkYXRhLmlkID4gMCkge1xyXG4gICAgICAgICAgICAgICAgc2V0T3BlbkNyZWF0ZU1lbWJlckRpYWxvZyhmYWxzZSlcclxuICAgICAgICAgICAgICAgIGNvbnN0IG5ld0RhdGEgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IGRhdGEuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw6IGRhdGEuZmlyc3ROYW1lICsgJyAnICsgZGF0YS5zdXJuYW1lLFxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgc2V0Q3Jld0xpc3QoWy4uLmNyZXdMaXN0LCBuZXdEYXRhXSlcclxuICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkSURzKFsuLi5zZWxlY3RlZElEcywgZGF0YS5pZF0pXHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZShbXHJcbiAgICAgICAgICAgICAgICAgICAgbmV3RGF0YSxcclxuICAgICAgICAgICAgICAgICAgICAuLi52YWx1ZS5tYXAoKGlkOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgY3JldyA9IGNyZXdMaXN0LmZpbmQoKGM6IGFueSkgPT4gYy52YWx1ZSA9PT0gaWQpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBjcmV3XHJcbiAgICAgICAgICAgICAgICAgICAgfSksXHJcbiAgICAgICAgICAgICAgICBdKVxyXG4gICAgICAgICAgICAgICAgc2V0RXJyb3IoZmFsc2UpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ2NyZWF0ZVVzZXIgZXJyb3InLCBlcnJvci5tZXNzYWdlKVxyXG4gICAgICAgICAgICBzZXRFcnJvcihlcnJvcilcclxuICAgICAgICB9LFxyXG4gICAgfSlcclxuXHJcbiAgICBjb25zdCBoYW5kbGVBZGROZXdNZW1iZXIgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgdmFyaWFibGVzID0ge1xyXG4gICAgICAgICAgICBpbnB1dDoge1xyXG4gICAgICAgICAgICAgICAgZmlyc3ROYW1lOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICdjcmV3LWZpcnN0TmFtZScsXHJcbiAgICAgICAgICAgICAgICAgICAgKSBhcyBIVE1MSW5wdXRFbGVtZW50XHJcbiAgICAgICAgICAgICAgICApLnZhbHVlXHJcbiAgICAgICAgICAgICAgICAgICAgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdjcmV3LWZpcnN0TmFtZScsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSBhcyBIVE1MSW5wdXRFbGVtZW50XHJcbiAgICAgICAgICAgICAgICAgICAgICApLnZhbHVlXHJcbiAgICAgICAgICAgICAgICAgICAgOiBudWxsLFxyXG4gICAgICAgICAgICAgICAgc3VybmFtZTogKFxyXG4gICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdjcmV3LXN1cm5hbWUnKSBhcyBIVE1MSW5wdXRFbGVtZW50XHJcbiAgICAgICAgICAgICAgICApLnZhbHVlXHJcbiAgICAgICAgICAgICAgICAgICAgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdjcmV3LXN1cm5hbWUnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICkgYXMgSFRNTElucHV0RWxlbWVudFxyXG4gICAgICAgICAgICAgICAgICAgICAgKS52YWx1ZVxyXG4gICAgICAgICAgICAgICAgICAgIDogbnVsbCxcclxuICAgICAgICAgICAgICAgIGVtYWlsOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2NyZXctZW1haWwnKSBhcyBIVE1MSW5wdXRFbGVtZW50XHJcbiAgICAgICAgICAgICAgICApLnZhbHVlXHJcbiAgICAgICAgICAgICAgICAgICAgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdjcmV3LWVtYWlsJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICApIGFzIEhUTUxJbnB1dEVsZW1lbnRcclxuICAgICAgICAgICAgICAgICAgICAgICkudmFsdWVcclxuICAgICAgICAgICAgICAgICAgICA6IG51bGwsXHJcbiAgICAgICAgICAgICAgICBwaG9uZU51bWJlcjogKFxyXG4gICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAnY3Jldy1waG9uZU51bWJlcicsXHJcbiAgICAgICAgICAgICAgICAgICAgKSBhcyBIVE1MSW5wdXRFbGVtZW50XHJcbiAgICAgICAgICAgICAgICApLnZhbHVlXHJcbiAgICAgICAgICAgICAgICAgICAgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdjcmV3LXBob25lTnVtYmVyJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICApIGFzIEhUTUxJbnB1dEVsZW1lbnRcclxuICAgICAgICAgICAgICAgICAgICAgICkudmFsdWVcclxuICAgICAgICAgICAgICAgICAgICA6IG51bGwsXHJcbiAgICAgICAgICAgICAgICB1c2VybmFtZTogKFxyXG4gICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdjcmV3LXVzZXJuYW1lJykgYXMgSFRNTElucHV0RWxlbWVudFxyXG4gICAgICAgICAgICAgICAgKS52YWx1ZVxyXG4gICAgICAgICAgICAgICAgICAgID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnY3Jldy11c2VybmFtZScsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSBhcyBIVE1MSW5wdXRFbGVtZW50XHJcbiAgICAgICAgICAgICAgICAgICAgICApLnZhbHVlXHJcbiAgICAgICAgICAgICAgICAgICAgOiBudWxsLFxyXG4gICAgICAgICAgICAgICAgcGFzc3dvcmQ6IChcclxuICAgICAgICAgICAgICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnY3Jldy1wYXNzd29yZCcpIGFzIEhUTUxJbnB1dEVsZW1lbnRcclxuICAgICAgICAgICAgICAgICkudmFsdWVcclxuICAgICAgICAgICAgICAgICAgICA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ2NyZXctcGFzc3dvcmQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICkgYXMgSFRNTElucHV0RWxlbWVudFxyXG4gICAgICAgICAgICAgICAgICAgICAgKS52YWx1ZVxyXG4gICAgICAgICAgICAgICAgICAgIDogbnVsbCxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKG9mZmxpbmUpIHtcclxuICAgICAgICAgICAgLy8gcXVlcnlBZGRNZW1iZXJcclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHNlYUxvZ3NNZW1iZXJNb2RlbC5zYXZlKHtcclxuICAgICAgICAgICAgICAgIC4uLnZhcmlhYmxlcy5pbnB1dCxcclxuICAgICAgICAgICAgICAgIGlkOiBnZW5lcmF0ZVVuaXF1ZUlkKCksXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIHNldE9wZW5DcmVhdGVNZW1iZXJEaWFsb2coZmFsc2UpXHJcbiAgICAgICAgICAgIGNvbnN0IG5ld0RhdGEgPSB7XHJcbiAgICAgICAgICAgICAgICB2YWx1ZTogZGF0YS5pZCxcclxuICAgICAgICAgICAgICAgIGxhYmVsOiBkYXRhLmZpcnN0TmFtZSArICcgJyArIGRhdGEuc3VybmFtZSxcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBzZXRDcmV3TGlzdChbLi4uY3Jld0xpc3QsIG5ld0RhdGFdKVxyXG4gICAgICAgICAgICBzZXRTZWxlY3RlZElEcyhbLi4uc2VsZWN0ZWRJRHMsIGRhdGEuaWRdKVxyXG4gICAgICAgICAgICBvbkNoYW5nZShbXHJcbiAgICAgICAgICAgICAgICBuZXdEYXRhLFxyXG4gICAgICAgICAgICAgICAgLi4udmFsdWUubWFwKChpZDogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgY3JldyA9IGNyZXdMaXN0LmZpbmQoKGM6IGFueSkgPT4gYy52YWx1ZSA9PT0gaWQpXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGNyZXdcclxuICAgICAgICAgICAgICAgIH0pLFxyXG4gICAgICAgICAgICBdKVxyXG4gICAgICAgICAgICBzZXRFcnJvcihmYWxzZSlcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBhd2FpdCBxdWVyeUFkZE1lbWJlcih7XHJcbiAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHZhcmlhYmxlcyxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBjb25zb2xlLmxvZyhcclxuICAgICAgICAgICAgJ0NyZXdNdWx0aVNlbGVjdERyb3Bkb3duIC0gU3luY2luZyB2YWx1ZTonLFxyXG4gICAgICAgICAgICB2YWx1ZSxcclxuICAgICAgICAgICAgJ2NyZXdMaXN0IGxlbmd0aDonLFxyXG4gICAgICAgICAgICBjcmV3TGlzdC5sZW5ndGgsXHJcbiAgICAgICAgKVxyXG5cclxuICAgICAgICAvLyBIYW5kbGUgZW1wdHkgdmFsdWUgLSBjbGVhciBzZWxlY3Rpb25cclxuICAgICAgICBpZiAoaXNFbXB0eSh2YWx1ZSkgfHwgdmFsdWUubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdDcmV3TXVsdGlTZWxlY3REcm9wZG93biAtIENsZWFyaW5nIHNlbGVjdGlvbicpXHJcbiAgICAgICAgICAgIHNldFNlbGVjdGVkSURzKFtdKVxyXG4gICAgICAgICAgICByZXR1cm5cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIFdhaXQgZm9yIGNyZXdMaXN0IHRvIGJlIHBvcHVsYXRlZFxyXG4gICAgICAgIGlmIChpc0VtcHR5KGNyZXdMaXN0KSkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcclxuICAgICAgICAgICAgICAgICdDcmV3TXVsdGlTZWxlY3REcm9wZG93biAtIFdhaXRpbmcgZm9yIGNyZXdMaXN0IHRvIGxvYWQnLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gQ29udmVydCB2YWx1ZSBhcnJheSB0byBwcm9wZXIgT3B0aW9uIG9iamVjdHNcclxuICAgICAgICBjb25zdCBzZWxlY3RlZE9wdGlvbnMgPSB2YWx1ZVxyXG4gICAgICAgICAgICAubWFwKChpZCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgLy8gSGFuZGxlIGJvdGggc3RyaW5nIGFuZCBudW1iZXIgSURzXHJcbiAgICAgICAgICAgICAgICBjb25zdCBub3JtYWxpemVkSWQgPSBTdHJpbmcoaWQpXHJcbiAgICAgICAgICAgICAgICBjb25zdCBvcHRpb24gPSBjcmV3TGlzdC5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgIChjcmV3OiBhbnkpID0+IFN0cmluZyhjcmV3LnZhbHVlKSA9PT0gbm9ybWFsaXplZElkLFxyXG4gICAgICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgICAgIGlmICghb3B0aW9uKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAnQ3Jld011bHRpU2VsZWN0RHJvcGRvd24gLSBDb3VsZCBub3QgZmluZCBjcmV3IHdpdGggSUQ6JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7IHZhbHVlOiBub3JtYWxpemVkSWQsIGxhYmVsOiBgVW5rbm93biAoJHtpZH0pYCB9XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIG9wdGlvblxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAuZmlsdGVyKEJvb2xlYW4pIC8vIFJlbW92ZSBhbnkgbnVsbC91bmRlZmluZWQgb3B0aW9uc1xyXG5cclxuICAgICAgICBjb25zb2xlLmxvZyhcclxuICAgICAgICAgICAgJ0NyZXdNdWx0aVNlbGVjdERyb3Bkb3duIC0gU2V0dGluZyBzZWxlY3RlZCBvcHRpb25zOicsXHJcbiAgICAgICAgICAgIHNlbGVjdGVkT3B0aW9ucyxcclxuICAgICAgICApXHJcbiAgICAgICAgc2V0U2VsZWN0ZWRJRHMoc2VsZWN0ZWRPcHRpb25zKVxyXG4gICAgfSwgW3ZhbHVlLCBjcmV3TGlzdF0pXHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8PlxyXG4gICAgICAgICAgICA8Q29tYm9ib3hcclxuICAgICAgICAgICAgICAgIG9wdGlvbnM9e2NyZXdMaXN0fVxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkSURzfSAvLyBVc2UgdmFsdWUgaW5zdGVhZCBvZiBkZWZhdWx0VmFsdWVzIGZvciBjb250cm9sbGVkIGNvbXBvbmVudFxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZU9uQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWxlY3QgQ3Jld1wiXHJcbiAgICAgICAgICAgICAgICBtdWx0aVxyXG4gICAgICAgICAgICAgICAgaXNMb2FkaW5nPXshY3Jld0xpc3R9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDxBZGRDcmV3TWVtYmVyRGlhbG9nXHJcbiAgICAgICAgICAgICAgICBvcGVuRGlhbG9nPXtvcGVuQ3JlYXRlTWVtYmVyRGlhbG9nfVxyXG4gICAgICAgICAgICAgICAgc2V0T3BlbkRpYWxvZz17c2V0T3BlbkNyZWF0ZU1lbWJlckRpYWxvZ31cclxuICAgICAgICAgICAgICAgIGhhbmRsZUNyZWF0ZT17aGFuZGxlQWRkTmV3TWVtYmVyfVxyXG4gICAgICAgICAgICAgICAgYWN0aW9uVGV4dD1cIkFkZCBDcmV3IE1lbWJlclwiXHJcbiAgICAgICAgICAgICAgICBlcnJvcj17ZXJyb3J9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgPC8+XHJcbiAgICApXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IENyZXdNdWx0aVNlbGVjdERyb3Bkb3duXHJcbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsImlzRW1wdHkiLCJDUkVBVEVfVVNFUiIsInVzZUxhenlRdWVyeSIsInVzZU11dGF0aW9uIiwiU2VhTG9nc01lbWJlck1vZGVsIiwiZ2VuZXJhdGVVbmlxdWVJZCIsIkNvbWJvYm94IiwiQWRkQ3Jld01lbWJlckRpYWxvZyIsIlJlYWRTZWFMb2dzTWVtYmVycyIsIkNyZXdNdWx0aVNlbGVjdERyb3Bkb3duIiwidmFsdWUiLCJvbkNoYW5nZSIsIm1lbWJlcklkT3B0aW9ucyIsImRlcGFydG1lbnRzIiwiZmlsdGVyQnlBZG1pbiIsIm9mZmxpbmUiLCJ2ZXNzZWxJRCIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImNyZXdMaXN0Iiwic2V0Q3Jld0xpc3QiLCJvcGVuQ3JlYXRlTWVtYmVyRGlhbG9nIiwic2V0T3BlbkNyZWF0ZU1lbWJlckRpYWxvZyIsInNlbGVjdGVkSURzIiwic2V0U2VsZWN0ZWRJRHMiLCJlcnJvciIsInNldEVycm9yIiwic2VhTG9nc01lbWJlck1vZGVsIiwiaGFuZGxlU2V0Q3Jld0xpc3QiLCJjcmV3TGlzdFJhdyIsInZlc3NlbENyZXdMaXN0IiwiZmlsdGVyIiwiY3JldyIsInZlaGljbGVzIiwibm9kZXMiLCJzb21lIiwidmVoaWNsZSIsImlkIiwiY3JlYXRlT3B0aW9uIiwibGFiZWwiLCJkYXRhIiwiY3Jld0lzQWRtaW4iLCJsZW5ndGgiLCJkZXBhcnRtZW50TGlzdCIsImZsYXRNYXAiLCJkZXBhcnRtZW50IiwiY3Jld3MiLCJub2RlIiwiaW5jbHVkZXMiLCJtYXAiLCJpdGVtIiwiZmlyc3ROYW1lIiwic3VybmFtZSIsImZpbHRlcmVkQ3Jld0xpc3QiLCJxdWVyeVNlYUxvZ3NNZW1iZXJzTGlzdCIsImZldGNoUG9saWN5Iiwib25FcnJvciIsImNvbnNvbGUiLCJsb2FkQ3Jld01lbWJlcnMiLCJhbGxNZW1iZXJzIiwib2Zmc2V0IiwibGltaXQiLCJoYXNOZXh0UGFnZSIsInJlc3BvbnNlIiwidmFyaWFibGVzIiwiaXNBcmNoaXZlZCIsImVxIiwicmVhZFNlYUxvZ3NNZW1iZXJzIiwicGFnZUluZm8iLCJnZXRBbGwiLCJ0aGVuIiwiZ3JvdXBzIiwicGVybWlzc2lvbiIsImNvZGUiLCJoYW5kbGVPbkNoYW5nZSIsInZhbHVlQXJyYXkiLCJBcnJheSIsImlzQXJyYXkiLCJmaW5kIiwib3B0aW9uIiwidmFsaWRPcHRpb25zIiwicXVlcnlBZGRNZW1iZXIiLCJvbkNvbXBsZXRlZCIsImNyZWF0ZVNlYUxvZ3NNZW1iZXIiLCJuZXdEYXRhIiwiYyIsIm1lc3NhZ2UiLCJoYW5kbGVBZGROZXdNZW1iZXIiLCJpbnB1dCIsImRvY3VtZW50IiwiZ2V0RWxlbWVudEJ5SWQiLCJlbWFpbCIsInBob25lTnVtYmVyIiwidXNlcm5hbWUiLCJwYXNzd29yZCIsInNhdmUiLCJsb2ciLCJzZWxlY3RlZE9wdGlvbnMiLCJub3JtYWxpemVkSWQiLCJTdHJpbmciLCJ3YXJuIiwiQm9vbGVhbiIsIm9wdGlvbnMiLCJwbGFjZWhvbGRlciIsIm11bHRpIiwib3BlbkRpYWxvZyIsInNldE9wZW5EaWFsb2ciLCJoYW5kbGVDcmVhdGUiLCJhY3Rpb25UZXh0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\n"));

/***/ })

});