"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/create/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx":
/*!***********************************************************************!*\
  !*** ./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx ***!
  \***********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _add_crew_member_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../add-crew-member-dialog */ \"(app-pages-browser)/./src/app/ui/crew/add-crew-member-dialog.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst CrewMultiSelectDropdown = (param)=>{\n    let { value = [], onChange, memberIdOptions = [], departments = [], filterByAdmin = false, offline = false, vesselID = 0 } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [crewList, setCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openCreateMemberDialog, setOpenCreateMemberDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIDs, setSelectedIDs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__[\"default\"]();\n    const handleSetCrewList = (crewListRaw)=>{\n        // If vesselID > 0, filter the crew list to display only the vessel crew.\n        const vesselCrewList = vesselID > 0 ? crewListRaw.filter((crew)=>crew.vehicles.nodes.some((vehicle)=>+vehicle.id === vesselID)) : crewListRaw;\n        const createOption = {\n            value: \"newCrewMember\",\n            label: \"--- Create Crew Member ---\"\n        };\n        const data = vesselCrewList.filter((crew)=>filterByAdmin ? !crewIsAdmin(crew) : true);\n        if (departments.length > 0) {\n            const departmentList = departments.flatMap((department)=>{\n                return department.id;\n            });\n            const crews = data.filter((crew)=>crew.departments.nodes.some((node)=>departmentList.includes(node.id))).map((item)=>{\n                var _item_firstName, _item_surname;\n                return {\n                    value: item.id,\n                    label: \"\".concat((_item_firstName = item.firstName) !== null && _item_firstName !== void 0 ? _item_firstName : \"\", \" \").concat((_item_surname = item.surname) !== null && _item_surname !== void 0 ? _item_surname : \"\")\n                };\n            });\n            if (memberIdOptions.length === 0) {\n                setCrewList([\n                    createOption,\n                    ...crews\n                ]);\n            } else {\n                const filteredCrewList = crews.filter((crew)=>{\n                    return memberIdOptions.includes(crew.value);\n                });\n                setCrewList(filteredCrewList);\n            }\n        } else {\n            const crews = data.map((item)=>{\n                var _item_firstName, _item_surname;\n                return {\n                    value: item.id,\n                    label: \"\".concat((_item_firstName = item.firstName) !== null && _item_firstName !== void 0 ? _item_firstName : \"\", \" \").concat((_item_surname = item.surname) !== null && _item_surname !== void 0 ? _item_surname : \"\")\n                };\n            });\n            if (memberIdOptions.length === 0) {\n                setCrewList([\n                    createOption,\n                    ...crews\n                ]);\n            } else {\n                const filteredCrewList = crews.filter((crew)=>{\n                    return memberIdOptions.includes(crew.value);\n                });\n                setCrewList(filteredCrewList);\n            }\n        }\n    };\n    const [querySeaLogsMembersList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_9__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_8__.ReadSeaLogsMembers, {\n        fetchPolicy: \"cache-and-network\",\n        onError: (error)=>{\n            console.error(\"querySeaLogsMembersList error\", error);\n        }\n    });\n    const loadCrewMembers = async ()=>{\n        let allMembers = [];\n        let offset = 0;\n        const limit = 100;\n        let hasNextPage = true;\n        try {\n            while(hasNextPage){\n                var _response_data;\n                const response = await querySeaLogsMembersList({\n                    variables: {\n                        filter: {\n                            isArchived: {\n                                eq: false\n                            }\n                        },\n                        limit: limit,\n                        offset: offset\n                    }\n                });\n                if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.readSeaLogsMembers) {\n                    const data = response.data.readSeaLogsMembers.nodes;\n                    const pageInfo = response.data.readSeaLogsMembers.pageInfo;\n                    if (data && data.length > 0) {\n                        allMembers = [\n                            ...allMembers,\n                            ...data\n                        ];\n                    }\n                    hasNextPage = (pageInfo === null || pageInfo === void 0 ? void 0 : pageInfo.hasNextPage) || false;\n                    offset += limit;\n                } else {\n                    hasNextPage = false;\n                }\n            }\n            // Set all collected members at once\n            if (allMembers.length > 0) {\n                handleSetCrewList(allMembers);\n            }\n        } catch (error) {\n            console.error(\"Error loading all crew members:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && !offline) {\n            loadCrewMembers();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading,\n        offline\n    ]);\n    // if (!offline) {\n    // getSeaLogsMembersList(handleSetCrewList)\n    // }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            seaLogsMemberModel.getAll().then((data)=>{\n                handleSetCrewList(data);\n            });\n        }\n    }, [\n        offline\n    ]);\n    const crewIsAdmin = (crew)=>{\n        var _crew_groups_nodes;\n        return ((_crew_groups_nodes = crew.groups.nodes) === null || _crew_groups_nodes === void 0 ? void 0 : _crew_groups_nodes.filter((permission)=>{\n            return permission.code === \"admin\";\n        }).length) > 0;\n    };\n    const handleOnChange = (value)=>{\n        console.log(\"CrewMultiSelectDropdown - handleOnChange called with:\", value);\n        if (!value) {\n            console.log(\"CrewMultiSelectDropdown - Clearing selection (null value)\");\n            setSelectedIDs([]);\n            onChange([]);\n            return;\n        }\n        const valueArray = Array.isArray(value) ? value : [\n            value\n        ];\n        // Fix the condition to properly check for 'newCrewMember'\n        if (valueArray.find((option)=>option.value === \"newCrewMember\")) {\n            console.log(\"CrewMultiSelectDropdown - Opening create member dialog\");\n            setOpenCreateMemberDialog(true);\n            return;\n        }\n        if (valueArray.length === 0) {\n            console.log(\"CrewMultiSelectDropdown - Clearing selection (empty array)\");\n            setSelectedIDs([]);\n            onChange([]);\n            return;\n        }\n        // Ensure we're working with valid Option objects\n        const validOptions = valueArray.filter((option)=>option && typeof option === \"object\");\n        console.log(\"CrewMultiSelectDropdown - Setting valid options:\", validOptions);\n        setSelectedIDs(validOptions);\n        onChange(validOptions);\n    };\n    const [queryAddMember] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_10__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CREATE_USER, {\n        fetchPolicy: \"no-cache\",\n        onCompleted: (response)=>{\n            const data = response.createSeaLogsMember;\n            if (data.id > 0) {\n                setOpenCreateMemberDialog(false);\n                const newData = {\n                    value: data.id,\n                    label: data.firstName + \" \" + data.surname\n                };\n                setCrewList([\n                    ...crewList,\n                    newData\n                ]);\n                setSelectedIDs([\n                    ...selectedIDs,\n                    data.id\n                ]);\n                onChange([\n                    newData,\n                    ...value.map((id)=>{\n                        const crew = crewList.find((c)=>c.value === id);\n                        return crew;\n                    })\n                ]);\n                setError(false);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createUser error\", error.message);\n            setError(error);\n        }\n    });\n    const handleAddNewMember = async ()=>{\n        const variables = {\n            input: {\n                firstName: document.getElementById(\"crew-firstName\").value ? document.getElementById(\"crew-firstName\").value : null,\n                surname: document.getElementById(\"crew-surname\").value ? document.getElementById(\"crew-surname\").value : null,\n                email: document.getElementById(\"crew-email\").value ? document.getElementById(\"crew-email\").value : null,\n                phoneNumber: document.getElementById(\"crew-phoneNumber\").value ? document.getElementById(\"crew-phoneNumber\").value : null,\n                username: document.getElementById(\"crew-username\").value ? document.getElementById(\"crew-username\").value : null,\n                password: document.getElementById(\"crew-password\").value ? document.getElementById(\"crew-password\").value : null\n            }\n        };\n        if (offline) {\n            // queryAddMember\n            const data = await seaLogsMemberModel.save({\n                ...variables.input,\n                id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_5__.generateUniqueId)()\n            });\n            setOpenCreateMemberDialog(false);\n            const newData = {\n                value: data.id,\n                label: data.firstName + \" \" + data.surname\n            };\n            setCrewList([\n                ...crewList,\n                newData\n            ]);\n            setSelectedIDs([\n                ...selectedIDs,\n                data.id\n            ]);\n            onChange([\n                newData,\n                ...value.map((id)=>{\n                    const crew = crewList.find((c)=>c.value === id);\n                    return crew;\n                })\n            ]);\n            setError(false);\n        } else {\n            await queryAddMember({\n                variables: variables\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"CrewMultiSelectDropdown - Syncing value:\", value, \"crewList length:\", crewList.length);\n        // Handle empty value - clear selection\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(value) || value.length === 0) {\n            console.log(\"CrewMultiSelectDropdown - Clearing selection\");\n            setSelectedIDs([]);\n            return;\n        }\n        // Wait for crewList to be populated\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(crewList)) {\n            console.log(\"CrewMultiSelectDropdown - Waiting for crewList to load\");\n            return;\n        }\n        // Convert value array to proper Option objects\n        const selectedOptions = value.map((id)=>{\n            // Handle both string and number IDs\n            const normalizedId = String(id);\n            const option = crewList.find((crew)=>String(crew.value) === normalizedId);\n            if (!option) {\n                console.warn(\"CrewMultiSelectDropdown - Could not find crew with ID:\", id);\n                return {\n                    value: normalizedId,\n                    label: \"Unknown (\".concat(id, \")\")\n                };\n            }\n            return option;\n        }).filter(Boolean) // Remove any null/undefined options\n        ;\n        console.log(\"CrewMultiSelectDropdown - Setting selected options:\", selectedOptions);\n        setSelectedIDs(selectedOptions);\n    }, [\n        value,\n        crewList\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_6__.Combobox, {\n                options: crewList,\n                value: selectedIDs,\n                onChange: handleOnChange,\n                placeholder: \"Select Crew\",\n                multi: true,\n                isLoading: !crewList\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\multiselect-dropdown\\\\multiselect-dropdown.tsx\",\n                lineNumber: 387,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_add_crew_member_dialog__WEBPACK_IMPORTED_MODULE_7__.AddCrewMemberDialog, {\n                openDialog: openCreateMemberDialog,\n                setOpenDialog: setOpenCreateMemberDialog,\n                handleCreate: handleAddNewMember,\n                actionText: \"Add Crew Member\",\n                error: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\multiselect-dropdown\\\\multiselect-dropdown.tsx\",\n                lineNumber: 395,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CrewMultiSelectDropdown, \"B5kTBW1ijKbUdQ1QBKs5KkaV5Fg=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_9__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_10__.useMutation\n    ];\n});\n_c = CrewMultiSelectDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewMultiSelectDropdown);\nvar _c;\n$RefreshReg$(_c, \"CrewMultiSelectDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\n"));

/***/ })

});