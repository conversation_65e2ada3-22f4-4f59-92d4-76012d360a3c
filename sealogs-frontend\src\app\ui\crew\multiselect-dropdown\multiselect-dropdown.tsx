'use client'
import { useEffect, useState } from 'react'
import { isEmpty } from 'lodash'
import { CREATE_USER } from '@/app/lib/graphQL/mutation'
import { useLazyQuery, useMutation } from '@apollo/client'

import SeaLogsMemberModel from '@/app/offline/models/seaLogsMember'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { Combobox, Option } from '@/components/ui/comboBox'
import { AddCrewMemberDialog } from '../add-crew-member-dialog'
import { ReadSeaLogsMembers } from './queries'

const CrewMultiSelectDropdown = ({
    value = [], // an array of crew IDs
    onChange,
    memberIdOptions = [],
    departments = [],
    filterByAdmin = false,
    offline = false,
    vesselID = 0,
}: {
    value: any[]
    onChange: any
    memberIdOptions?: any[]
    departments?: any
    filterByAdmin?: boolean
    offline?: boolean
    vesselID?: number
}) => {
    const [isLoading, setIsLoading] = useState(true)
    const [crewList, setCrewList] = useState([] as any)
    const [openCreateMemberDialog, setOpenCreateMemberDialog] = useState(false)
    const [selectedIDs, setSelectedIDs] = useState([] as any)
    const [error, setError] = useState<any>(false)
    const seaLogsMemberModel = new SeaLogsMemberModel()
    const handleSetCrewList = (crewListRaw: any) => {
        // If vesselID > 0, filter the crew list to display only the vessel crew.
        const vesselCrewList =
            vesselID > 0
                ? crewListRaw.filter((crew: any) =>
                      crew.vehicles.nodes.some(
                          (vehicle: any) => +vehicle.id === vesselID,
                      ),
                  )
                : crewListRaw
        const createOption = {
            value: 'newCrewMember',
            label: '--- Create Crew Member ---',
        }
        const data = vesselCrewList.filter((crew: any) =>
            filterByAdmin ? !crewIsAdmin(crew) : true,
        )
        if (departments.length > 0) {
            const departmentList = departments.flatMap((department: any) => {
                return department.id
            })
            const crews = data
                .filter((crew: any) =>
                    crew.departments.nodes.some((node: any) =>
                        departmentList.includes(node.id),
                    ),
                )
                .map((item: any) => {
                    return {
                        value: item.id,
                        label: `${item.firstName ?? ''} ${item.surname ?? ''}`,
                    }
                })
            if (memberIdOptions.length === 0) {
                setCrewList([createOption, ...crews])
            } else {
                const filteredCrewList = crews.filter((crew: any) => {
                    return memberIdOptions.includes(crew.value)
                })
                setCrewList(filteredCrewList)
            }
        } else {
            const crews = data.map((item: any) => {
                return {
                    value: item.id,
                    label: `${item.firstName ?? ''} ${item.surname ?? ''}`,
                }
            })
            if (memberIdOptions.length === 0) {
                setCrewList([createOption, ...crews])
            } else {
                const filteredCrewList = crews.filter((crew: any) => {
                    return memberIdOptions.includes(crew.value)
                })
                setCrewList(filteredCrewList)
            }
        }
    }
    const [querySeaLogsMembersList] = useLazyQuery(ReadSeaLogsMembers, {
        fetchPolicy: 'cache-and-network',
        onError: (error: any) => {
            console.error('querySeaLogsMembersList error', error)
        },
    })
    const loadCrewMembers = async () => {
        let allMembers: any[] = []
        let offset = 0
        const limit = 100
        let hasNextPage = true

        try {
            while (hasNextPage) {
                const response = await querySeaLogsMembersList({
                    variables: {
                        filter: { isArchived: { eq: false } },
                        limit: limit,
                        offset: offset,
                    },
                })

                if (response.data?.readSeaLogsMembers) {
                    const data = response.data.readSeaLogsMembers.nodes
                    const pageInfo = response.data.readSeaLogsMembers.pageInfo

                    if (data && data.length > 0) {
                        allMembers = [...allMembers, ...data]
                    }

                    hasNextPage = pageInfo?.hasNextPage || false
                    offset += limit
                } else {
                    hasNextPage = false
                }
            }

            // Set all collected members at once
            if (allMembers.length > 0) {
                handleSetCrewList(allMembers)
            }
        } catch (error) {
            console.error('Error loading all crew members:', error)
        }
    }
    useEffect(() => {
        if (isLoading && !offline) {
            loadCrewMembers()
            setIsLoading(false)
        }
    }, [isLoading, offline])
    // if (!offline) {
    // getSeaLogsMembersList(handleSetCrewList)
    // }

    useEffect(() => {
        if (offline) {
            seaLogsMemberModel.getAll().then((data: any) => {
                handleSetCrewList(data)
            })
        }
    }, [offline])

    const crewIsAdmin = (crew: any) => {
        return (
            crew.groups.nodes?.filter((permission: any) => {
                return permission.code === 'admin'
            }).length > 0
        )
    }

    const handleOnChange = (value: Option | Option[] | null) => {
        if (!value) {
            setSelectedIDs([])
            onChange([])
            return
        }

        const valueArray = Array.isArray(value) ? value : [value]

        // Fix the condition to properly check for 'newCrewMember'
        if (
            valueArray.find((option: any) => option.value === 'newCrewMember')
        ) {
            setOpenCreateMemberDialog(true)
            return
        }

        if (valueArray.length === 0) {
            setSelectedIDs([])
            onChange([])
            return
        }

        // Ensure we're working with valid Option objects
        const validOptions = valueArray.filter(
            (option) => option && typeof option === 'object',
        )

        setSelectedIDs(validOptions)
        onChange(validOptions)
    }

    const [queryAddMember] = useMutation(CREATE_USER, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.createSeaLogsMember
            if (data.id > 0) {
                setOpenCreateMemberDialog(false)
                const newData = {
                    value: data.id,
                    label: data.firstName + ' ' + data.surname,
                }
                setCrewList([...crewList, newData])
                setSelectedIDs([...selectedIDs, data.id])
                onChange([
                    newData,
                    ...value.map((id: any) => {
                        const crew = crewList.find((c: any) => c.value === id)
                        return crew
                    }),
                ])
                setError(false)
            }
        },
        onError: (error: any) => {
            console.error('createUser error', error.message)
            setError(error)
        },
    })

    const handleAddNewMember = async () => {
        const variables = {
            input: {
                firstName: (
                    document.getElementById(
                        'crew-firstName',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-firstName',
                          ) as HTMLInputElement
                      ).value
                    : null,
                surname: (
                    document.getElementById('crew-surname') as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-surname',
                          ) as HTMLInputElement
                      ).value
                    : null,
                email: (
                    document.getElementById('crew-email') as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-email',
                          ) as HTMLInputElement
                      ).value
                    : null,
                phoneNumber: (
                    document.getElementById(
                        'crew-phoneNumber',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-phoneNumber',
                          ) as HTMLInputElement
                      ).value
                    : null,
                username: (
                    document.getElementById('crew-username') as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-username',
                          ) as HTMLInputElement
                      ).value
                    : null,
                password: (
                    document.getElementById('crew-password') as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-password',
                          ) as HTMLInputElement
                      ).value
                    : null,
            },
        }
        if (offline) {
            // queryAddMember
            const data = await seaLogsMemberModel.save({
                ...variables.input,
                id: generateUniqueId(),
            })
            setOpenCreateMemberDialog(false)
            const newData = {
                value: data.id,
                label: data.firstName + ' ' + data.surname,
            }
            setCrewList([...crewList, newData])
            setSelectedIDs([...selectedIDs, data.id])
            onChange([
                newData,
                ...value.map((id: any) => {
                    const crew = crewList.find((c: any) => c.value === id)
                    return crew
                }),
            ])
            setError(false)
        } else {
            await queryAddMember({
                variables: variables,
            })
        }
    }

    useEffect(() => {
        // Handle empty value - clear selection
        if (isEmpty(value) || value.length === 0) {
            setSelectedIDs([])
            return
        }

        // Wait for crewList to be populated
        if (isEmpty(crewList)) {
            return
        }

        // Convert value array to proper Option objects
        const selectedOptions = value
            .map((id) => {
                // Handle both string and number IDs
                const normalizedId = String(id)
                const option = crewList.find(
                    (crew: any) => String(crew.value) === normalizedId,
                )

                if (!option) {
                    console.warn(
                        'CrewMultiSelectDropdown - Could not find crew with ID:',
                        id,
                    )
                    return { value: normalizedId, label: `Unknown (${id})` }
                }

                return option
            })
            .filter(Boolean) // Remove any null/undefined options

        setSelectedIDs(selectedOptions)
    }, [value, crewList])

    return (
        <>
            <Combobox
                options={crewList}
                value={selectedIDs} // Use value instead of defaultValues for controlled component
                onChange={handleOnChange}
                placeholder="Select Crew"
                multi
                responsiveBadges
                isLoading={!crewList}
            />
            <AddCrewMemberDialog
                openDialog={openCreateMemberDialog}
                setOpenDialog={setOpenCreateMemberDialog}
                handleCreate={handleAddNewMember}
                actionText="Add Crew Member"
                error={error}
            />
        </>
    )
}

export default CrewMultiSelectDropdown
