"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx":
/*!*****************************************!*\
  !*** ./src/app/ui/logbook/trip-log.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TripLog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash/uniqueId */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqueId.js\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/client */ \"(app-pages-browser)/./src/app/offline/models/client.js\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_eventType__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/models/eventType */ \"(app-pages-browser)/./src/app/offline/models/eventType.js\");\n/* harmony import */ var _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/offline/models/tripReport_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/tripReport_LogBookEntrySection.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_accordion__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/accordion */ \"(app-pages-browser)/./src/components/ui/accordion.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _depart_time__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./depart-time */ \"(app-pages-browser)/./src/app/ui/logbook/depart-time.tsx\");\n/* harmony import */ var _exp_arrival_time__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./exp-arrival-time */ \"(app-pages-browser)/./src/app/ui/logbook/exp-arrival-time.tsx\");\n/* harmony import */ var _actual_arrival_time__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./actual-arrival-time */ \"(app-pages-browser)/./src/app/ui/logbook/actual-arrival-time.tsx\");\n/* harmony import */ var _events__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./events */ \"(app-pages-browser)/./src/app/ui/logbook/events.tsx\");\n/* harmony import */ var _trip_log_pob__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./trip-log-pob */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-pob.tsx\");\n/* harmony import */ var _trip_log_vob__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./trip-log-vob */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-vob.tsx\");\n/* harmony import */ var _trip_log_dgr__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./trip-log-dgr */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-dgr.tsx\");\n/* harmony import */ var _components_trip_comments__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./components/trip-comments */ \"(app-pages-browser)/./src/app/ui/logbook/components/trip-comments.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_master__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! ./components/master */ \"(app-pages-browser)/./src/app/ui/logbook/components/master.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// React and Next.js imports\n\n\n\n\n\n\n// Utility imports\n\n\n\n\n\n\n// Model imports\n\n\n\n\n\n// UI Component imports\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Reusable TripReportAccordionContent component\nconst TripReportAccordionContent = (param)=>{\n    let { currentTrip, offline, tripReport, updateTripReport, updateTripReport_LogBookEntrySection, currentTripRef, locations, locked, edit_tripReport, vessel, crewMembers, logBookConfig, client, canCarryVehicles, canCarryDangerousGoods, selectedDGR, displayDangerousGoods, setDisplayDangerousGoods, displayDangerousGoodsSailing, setDisplayDangerousGoodsSailing, allDangerousGoods, setAllDangerousGoods, logBookStartDate, masterID, vessels, setSelectedRowEvent, setCurrentEventTypeEvent, setCurrentStopEvent, currentEventTypeEvent, currentStopEvent, tripReport_Stops, setTripReport_Stops, displayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpd, displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpdSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGRPVPD, setSelectedDGRPVPD, fuelLogs, comment, setComment, displayFieldTripLog, signatureKey, signature, setSignature, handleCancel, handleSave } = param;\n    var _currentTripRef_current;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_34__.cn)(\"space-y-6\", locked || !edit_tripReport ? \"opacity-70 pointer-events-none\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col inset-y-0 items-center pt-10 pb-4 absolute -left-5 sm:-left-[25px] min-h-24 w-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_34__.cn)(\"size-[11px] z-10 rounded-full\", \"border border-neutral-400 bg-background\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_34__.cn)(\"border-l h-full border-wedgewood-200 border-dashed\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_34__.cn)(\"size-[11px] z-10 rounded-full\", \"border border-neutral-400 bg-background\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_depart_time__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        offline: offline,\n                        currentTrip: currentTrip,\n                        tripReport: tripReport,\n                        templateStyle: \"\",\n                        updateTripReport: updateTripReport\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_21__.Label, {\n                        label: \"Departure location\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            offline: offline,\n                            setCurrentLocation: (location)=>{\n                            // Store coordinates if needed for direct coordinate input\n                            },\n                            handleLocationChange: (selectedLocation)=>{\n                                // Update the from location\n                                if (offline) {\n                                    updateTripReport({\n                                        id: [\n                                            ...tripReport.map((trip)=>trip.id),\n                                            currentTrip.id\n                                        ],\n                                        currentTripID: currentTrip.id,\n                                        key: \"fromLocationID\",\n                                        value: selectedLocation.value,\n                                        label: selectedLocation.label\n                                    });\n                                } else {\n                                    var _currentTripRef_current;\n                                    // For online mode, use the mutation\n                                    updateTripReport_LogBookEntrySection({\n                                        variables: {\n                                            input: {\n                                                id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                                                fromLocationID: selectedLocation.value\n                                            }\n                                        }\n                                    });\n                                }\n                            },\n                            currentEvent: {\n                                geoLocationID: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLocationID) || 0,\n                                lat: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLat) || 0,\n                                long: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLong) || 0\n                            },\n                            showAddNewLocation: true,\n                            showUseCoordinates: true,\n                            showCurrentLocation: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_20__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.H5, {\n                        children: \"PEOPLE ON BOARD\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_pob__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                        offline: offline,\n                        currentTrip: currentTrip,\n                        tripReport: tripReport,\n                        vessel: vessel,\n                        crewMembers: crewMembers,\n                        logBookConfig: logBookConfig,\n                        masterTerm: client === null || client === void 0 ? void 0 : client.masterTerm,\n                        updateTripReport: updateTripReport\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_20__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            canCarryVehicles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_vob__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                offline: offline,\n                                currentTrip: currentTrip,\n                                logBookConfig: logBookConfig\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 25\n                            }, undefined),\n                            canCarryDangerousGoods && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_dgr__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                offline: offline,\n                                locked: locked || !edit_tripReport,\n                                currentTrip: currentTrip,\n                                logBookConfig: logBookConfig,\n                                selectedDGR: selectedDGR,\n                                members: crewMembers,\n                                displayDangerousGoods: displayDangerousGoods,\n                                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                                allDangerousGoods: allDangerousGoods,\n                                setAllDangerousGoods: setAllDangerousGoods\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_20__.Separator, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_events__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        offline: offline,\n                        logBookStartDate: logBookStartDate,\n                        currentTrip: currentTrip,\n                        logBookConfig: logBookConfig,\n                        updateTripReport: updateTripReport,\n                        locked: locked,\n                        geoLocations: locations,\n                        tripReport: tripReport,\n                        crewMembers: crewMembers,\n                        masterID: masterID,\n                        vessel: vessel,\n                        vessels: vessels,\n                        setSelectedRow: setSelectedRowEvent,\n                        setCurrentEventType: setCurrentEventTypeEvent,\n                        setCurrentStop: setCurrentStopEvent,\n                        currentEventType: currentEventTypeEvent,\n                        currentStop: currentStopEvent,\n                        tripReport_Stops: tripReport_Stops,\n                        setTripReport_Stops: setTripReport_Stops,\n                        displayDangerousGoodsPvpd: displayDangerousGoodsPvpd,\n                        setDisplayDangerousGoodsPvpd: setDisplayDangerousGoodsPvpd,\n                        displayDangerousGoodsPvpdSailing: displayDangerousGoodsPvpdSailing,\n                        setDisplayDangerousGoodsPvpdSailing: setDisplayDangerousGoodsPvpdSailing,\n                        allPVPDDangerousGoods: allPVPDDangerousGoods,\n                        setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                        selectedDGRPVPD: selectedDGRPVPD,\n                        setSelectedDGRPVPD: setSelectedDGRPVPD,\n                        fuelLogs: fuelLogs\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_20__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_21__.Label, {\n                        label: \"Arrival location\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            offline: offline,\n                            setCurrentLocation: (location)=>{\n                            // Store coordinates if needed for direct coordinate input\n                            },\n                            handleLocationChange: (selectedLoc)=>{\n                                // Update the to location\n                                if (offline) {\n                                    updateTripReport({\n                                        id: [\n                                            ...tripReport.map((trip)=>trip.id),\n                                            currentTrip.id\n                                        ],\n                                        currentTripID: currentTrip.id,\n                                        key: \"toLocationID\",\n                                        value: selectedLoc.value,\n                                        label: selectedLoc.label\n                                    });\n                                } else {\n                                    var _currentTripRef_current;\n                                    // For online mode, use the mutation\n                                    updateTripReport_LogBookEntrySection({\n                                        variables: {\n                                            input: {\n                                                id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                                                toLocationID: selectedLoc.value\n                                            }\n                                        }\n                                    });\n                                }\n                            },\n                            currentEvent: {\n                                geoLocationID: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLocationID) || 0,\n                                lat: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLat) || 0,\n                                long: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLong) || 0\n                            },\n                            showAddNewLocation: true,\n                            showUseCoordinates: true,\n                            showCurrentLocation: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 130,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_exp_arrival_time__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 339,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_actual_arrival_time__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 346,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_trip_comments__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                setCommentField: setComment,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 353,\n                columnNumber: 13\n            }, undefined),\n            displayFieldTripLog(\"MasterID\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked || !edit_tripReport ? \"pointer-events-none\" : \"\", \" max-w-sm\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_master__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                    offline: offline,\n                    currentTrip: currentTrip,\n                    tripReport: tripReport,\n                    crewMembers: crewMembers,\n                    updateTripReport: updateTripReport\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 361,\n                columnNumber: 17\n            }, undefined),\n            displayFieldTripLog(\"Signature\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                locked: locked,\n                title: \"Signature Confirmation\",\n                description: \"By signing below, I confirm that the recorded entries are accurate to the best of my knowledge and in accordance with the vessel's operating procedures and regulations.\",\n                signature: currentTripRef === null || currentTripRef === void 0 ? void 0 : (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.sectionSignature,\n                onSignatureChanged: (sign)=>{\n                    setSignature(sign);\n                }\n            }, \"\".concat(signatureKey, \"-\").concat(currentTrip.id), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 374,\n                columnNumber: 17\n            }, undefined),\n            !locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_35__.FormFooter, {\n                className: \"justify-end gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"],\n                        onClick: handleCancel,\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"],\n                        onClick: handleSave,\n                        children: \"Update\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 387,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n        lineNumber: 123,\n        columnNumber: 9\n    }, undefined);\n};\n_c = TripReportAccordionContent;\nfunction TripLog(param) {\n    let { tripReport = false, logBookConfig, updateTripReport, locked, crewMembers, masterID, createdTab = false, setCreatedTab, currentTrip = false, setCurrentTrip, vessels, offline = false, fuelLogs, logBookStartDate } = param;\n    var _tripReport_find;\n    _s();\n    // const router = useRouter()\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    var _searchParams_get1;\n    const vesselID = (_searchParams_get1 = searchParams.get(\"vesselID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__.useToast)();\n    // Use nuqs to manage the tab state through URL query parameters\n    const [tab, setTab] = (0,nuqs__WEBPACK_IMPORTED_MODULE_39__.useQueryState)(\"tab\", {\n        defaultValue: \"crew\"\n    });\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [eventTypes, setEventTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openTripModal, setOpenTripModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [bufferTripID, setBufferTripID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [vessel, setVessel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [accordionValue, setAccordionValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedDGR, setSelectedDGR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [openRiskAnalysis, setOpenTripStartRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoods, setDisplayDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsSailing, setDisplayDangerousGoodsSailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpdSailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // const [openEventModal, setOpenEventModal] = useState(false)\n    // const [selectedRowdgr, setSelectedRowdgr] = useState<any>(false)\n    const [tripReport_Stops, setTripReport_Stops] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDGRPVPD, setSelectedDGRPVPD] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [allPVPDDangerousGoods, setAllPVPDDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRowEvent, setSelectedRowEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [riskBufferEvDgr, setRiskBufferEvDgr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allDangerousGoods, setAllDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentEventTypeEvent, setCurrentEventTypeEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStopEvent, setCurrentStopEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [signature, setSignature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [signatureKey, setSignatureKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default()());\n    const currentTripRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [deleteTripItem, setDeleteTripItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteTripConfirmation, setDeleteTripConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const canCarryDangerousGoods = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _vessel_vesselSpecifics;\n        return !!(vessel === null || vessel === void 0 ? void 0 : (_vessel_vesselSpecifics = vessel.vesselSpecifics) === null || _vessel_vesselSpecifics === void 0 ? void 0 : _vessel_vesselSpecifics.carriesDangerousGoods);\n    }, [\n        vessel\n    ]);\n    const canCarryVehicles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _vessel_vesselSpecifics;\n        return !!(vessel === null || vessel === void 0 ? void 0 : (_vessel_vesselSpecifics = vessel.vesselSpecifics) === null || _vessel_vesselSpecifics === void 0 ? void 0 : _vessel_vesselSpecifics.carriesVehicles);\n    }, [\n        vessel\n    ]);\n    // Initialize client\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getOneClient)(setClient);\n    }\n    // Update signature state when currentTrip changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentTrip && currentTrip.sectionSignature) {\n            setSignature(currentTrip.sectionSignature.signatureData || \"\");\n        } else {\n            setSignature(\"\");\n        }\n    }, [\n        currentTrip\n    ]);\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(tripReport ? tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find === void 0 ? void 0 : _tripReport_find.comment : \"\");\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_tripReport, setEdit_tripReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const clientModel = new _app_offline_models_client__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const eventTypeModel = new _app_offline_models_eventType__WEBPACK_IMPORTED_MODULE_14__[\"default\"]();\n    const tripReportModel = new _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__[\"default\"]();\n    const [openTripSelectionDialog, setOpenTripSelectionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tripReportSchedules, setTripReportSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTripReportSchedule, setSelectedTripReportSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tripScheduleServices, setTripScheduleServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTripScheduleServiceID, setSelectedTripScheduleServiceID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showNextTrips, setShowNextTrips] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreatingScheduledTrip, setIsCreatingScheduledTrip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"EDIT_LOGBOOKENTRY_TRIPREPORT\", permissions)) {\n                setEdit_tripReport(true);\n            } else {\n                setEdit_tripReport(false);\n            }\n        }\n    };\n    const offlineLoad = async ()=>{\n        const locations = await geoLocationModel.getAll();\n        setLocations(locations);\n        const types = await eventTypeModel.getAll();\n        setEventTypes(types);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.getPermissions);\n        loadTripScheduleServices();\n        if (!locations) {\n            if (offline) {\n                offlineLoad();\n            } else {\n                loadLocations();\n                loadEventTypes();\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (createdTab) {\n            setSelectedTab(createdTab);\n        }\n    }, [\n        createdTab\n    ]);\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getVesselByID)(+vesselID, setVessel);\n    }\n    const scrollToAccordionItem = (tripId)=>{\n        const element = document.getElementById(\"triplog-\".concat(tripId));\n        if (element) {\n            element.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (tripReport && currentTrip) {\n            const trip = tripReport.find((trip)=>trip.id === currentTrip.id);\n            currentTripRef.current = trip;\n            setCurrentTrip(trip);\n        }\n        if (tripReport && bufferTripID > 0) {\n            const trip = tripReport.find((trip)=>trip.id === bufferTripID);\n            if (trip) {\n                currentTripRef.current = trip;\n                setCurrentTrip(trip);\n                // Only expand accordion and scroll for regular trips, not scheduled trips\n                if (!selectedTripReportSchedule) {\n                    setAccordionValue(trip.id.toString());\n                    scrollToAccordionItem(trip.id);\n                }\n                setOpenTripModal(true);\n                setSelectedTab(trip.id);\n                setSignatureKey(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default()());\n                // Initialize signature data if available\n                if (trip.sectionSignature) {\n                    setSignature(trip.sectionSignature.signatureData || \"\");\n                } else {\n                    setSignature(\"\");\n                }\n                // Initialize trip-specific state\n                setRiskBufferEvDgr(trip === null || trip === void 0 ? void 0 : trip.dangerousGoodsChecklist);\n                setOpenTripStartRiskAnalysis(false);\n                setAllDangerousGoods(false);\n                setCurrentStopEvent(false);\n                setCurrentEventTypeEvent(false);\n                setSelectedRowEvent(false);\n                setDisplayDangerousGoods((trip === null || trip === void 0 ? void 0 : trip.enableDGR) === true);\n                setDisplayDangerousGoodsSailing((trip === null || trip === void 0 ? void 0 : trip.designatedDangerousGoodsSailing) === true);\n                setDisplayDangerousGoodsPvpd(false);\n                setDisplayDangerousGoodsPvpdSailing(null);\n                setAllPVPDDangerousGoods(false);\n                setSelectedDGRPVPD(false);\n                setTripReport_Stops(false);\n            }\n            setBufferTripID(0);\n        }\n    }, [\n        tripReport\n    ]);\n    const [loadLocations] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_GEO_LOCATIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setLocations(response.readGeoLocations.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading locations\", error);\n        }\n    });\n    const [loadEventTypes] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_EVENT_TYPES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setEventTypes(response.readEventTypes.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading activity types\", error);\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripReport_Stop, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const handleCreateTripReportScheduleStops = async (logBookEntrySectionID)=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(selectedTripReportSchedule)) {\n            const tripStops = selectedTripReportSchedule.tripReportScheduleStops.nodes || [];\n            await Promise.all(tripStops.map(async (stop)=>{\n                const input = {\n                    logBookEntrySectionID: logBookEntrySectionID,\n                    tripReportScheduleStopID: stop.id,\n                    arriveTime: stop.arriveTime,\n                    departTime: stop.departTime,\n                    stopLocationID: stop.stopLocationID\n                };\n                await createTripReport_Stop({\n                    variables: {\n                        input: input\n                    }\n                });\n            }));\n            setSelectedTripReportSchedule(null);\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        logBookEntrySectionID\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        logBookEntrySectionID\n                    ]\n                });\n            }\n        }\n    };\n    const [createTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripReport_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_LogBookEntrySection;\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ]\n                });\n            }\n            setCreatedTab(data.id);\n            setBufferTripID(data.id);\n            handleCreateTripReportScheduleStops(data.id);\n            // Always set accordion value and scroll for newly created trips\n            setAccordionValue(data.id.toString());\n            scrollToAccordionItem(data.id);\n            // Reset the flag\n            setIsCreatingScheduledTrip(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip report\", error);\n        }\n    });\n    const [updateTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateTripReport_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_LogBookEntrySection;\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            }\n            setBufferTripID(data.id);\n            setCurrentTrip(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip report\", error);\n        }\n    });\n    const [readTripReportSchedules, { loading: readTripReportSchedulesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.ReadTripReportSchedules, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripReportSchedules.nodes.filter((trip)=>// only show trips for the current vessel\n                trip.vehicles.nodes.some((vehicle)=>+vehicle.id === +vesselID));\n            if (showNextTrips) {\n                // only show 1 past trip and 4 upcoming trips\n                const currentTime = dayjs__WEBPACK_IMPORTED_MODULE_5___default()().format(\"HH:mm:ss\");\n                const pastIndex = data.findIndex((trip)=>trip.departTime >= currentTime);\n                const result = (pastIndex > 0 ? [\n                    data[pastIndex - 1]\n                ] : []).concat(data.slice(pastIndex, pastIndex + 4));\n                setTripReportSchedules(result);\n            } else {\n                setTripReportSchedules(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error loading TripReportSchedules\", error);\n        }\n    });\n    const [readTripScheduleServices] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.ReadTripScheduleServices, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripScheduleServices.nodes.map((tss)=>{\n                return {\n                    label: tss.title,\n                    value: tss.id\n                };\n            });\n            setTripScheduleServices(data);\n            setTripReportSchedules([]);\n        // setOpenTripSelectionDialog(true)\n        },\n        onError: (error)=>{\n            console.error(\"Error loading TripScheduleServices\", error);\n        }\n    });\n    const loadTripScheduleServices = async ()=>{\n        await readTripScheduleServices({\n            variables: {\n                filter: {\n                    vehicles: {\n                        id: {\n                            eq: vesselID\n                        }\n                    }\n                }\n            }\n        });\n    };\n    const loadTripReportSchedules = async (tripScheduleServiceID)=>{\n        setTripReportSchedules([]);\n        await readTripReportSchedules({\n            variables: {\n                filter: {\n                    // archived: { eq: false },\n                    // start: { eq: logBookStartDate },\n                    tripScheduleServiceID: {\n                        eq: tripScheduleServiceID\n                    }\n                }\n            }\n        });\n    };\n    const doCreateTripReport = async function(input) {\n        let isScheduledTrip = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!edit_tripReport) {\n            toast({\n                title: \"Error\",\n                description: \"You do not have permission to add a trip\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setOpenTripModal(false);\n        setCurrentTrip(false);\n        setIsCreatingScheduledTrip(isScheduledTrip);\n        if (offline) {\n            const data = await tripReportModel.save({\n                ...input,\n                id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)()\n            });\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ]\n                });\n            }\n            setCreatedTab(data.id);\n            setBufferTripID(data.id);\n            // For offline mode, immediately set accordion value since the trip is created synchronously\n            setAccordionValue(data.id.toString());\n            // Reset the flag\n            setIsCreatingScheduledTrip(false);\n        } else {\n            createTripReport_LogBookEntrySection({\n                variables: {\n                    input: input\n                }\n            });\n        }\n        setRiskBufferEvDgr(false);\n        setOpenTripStartRiskAnalysis(false);\n        setAllDangerousGoods(false);\n        setCurrentStopEvent(false);\n        setCurrentEventTypeEvent(false);\n        setSelectedRowEvent(false);\n        setDisplayDangerousGoods(false);\n        setDisplayDangerousGoodsSailing(false);\n        setDisplayDangerousGoodsPvpd(false);\n        setDisplayDangerousGoodsPvpdSailing(null);\n        setAllPVPDDangerousGoods(false);\n        setSelectedDGRPVPD(false);\n        setTripReport_Stops(false);\n    // setSelectedTripScheduleServiceID(null)\n    // setTripReportSchedules([])\n    // setShowNextTrips(false)\n    };\n    const handleAddTrip = async ()=>{\n        const allowedVesselTypes = [\n            \"SLALL\",\n            \"Tug_Boat\",\n            \"Passenger_Ferry\",\n            \"Water_Taxi\"\n        ];\n        if (allowedVesselTypes.includes(vessel.vesselType)) {\n            loadTripScheduleServices();\n        } else {\n            handleCustomTrip();\n        }\n    };\n    const handleCustomTrip = ()=>{\n        setOpenTripSelectionDialog(false);\n        const input = {\n            logBookEntryID: logentryID\n        };\n        doCreateTripReport(input, false) // false indicates this is a regular trip\n        ;\n    };\n    // Removed unused handleEditTrip function\n    const [createLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateLogBookEntrySection_Signature, {\n        onCompleted: (response)=>{\n            const data = response.createLogBookEntrySection_Signature;\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: +data.logBookEntrySectionID,\n                        sectionSignatureID: +(data === null || data === void 0 ? void 0 : data.id)\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error saving signature\", error);\n        }\n    });\n    const [updateLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateLogBookEntrySection_Signature, {\n        onCompleted: (response)=>{\n            var _currentTripRef_current;\n            const data = response.updateLogBookEntrySection_Signature;\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                        sectionSignatureID: +(data === null || data === void 0 ? void 0 : data.id)\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"341 TripLog updateLogBookEntrySection_Signature\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        // Log signature information for debugging\n        // Ensure we have a valid signature\n        const sigVariables = {\n            logBookEntrySectionID: currentTrip.id,\n            memberID: localStorage.getItem(\"userId\"),\n            signatureData: signature || \"\"\n        };\n        if (+currentTrip.sectionSignatureID > 0) {\n            // Update signature\n            updateLogBookEntrySection_Signature({\n                variables: {\n                    input: {\n                        ...sigVariables,\n                        id: +currentTrip.sectionSignatureID\n                    }\n                }\n            });\n        } else {\n            // Create signature\n            createLogBookEntrySection_Signature({\n                variables: {\n                    input: sigVariables !== null && sigVariables !== void 0 ? sigVariables : \"\"\n                }\n            });\n        }\n        if (offline) {\n            // updateTripReport_LogBookEntrySection\n            const data = await tripReportModel.save({\n                id: currentTrip.id,\n                comment: comment || null\n            });\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            }\n            setBufferTripID(data.id);\n        } else {\n            var _currentTripRef_current;\n            await updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                        comment: comment || null\n                    }\n                }\n            });\n            setOpenTripModal(false);\n            setCurrentTrip(false);\n        }\n    };\n    const displayFieldTripLog = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _dailyChecks__customisedComponentFields, _dailyChecks_;\n        const dailyChecks = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"TripReport_LogBookComponent\");\n        if ((dailyChecks === null || dailyChecks === void 0 ? void 0 : dailyChecks.length) > 0 && ((_dailyChecks_ = dailyChecks[0]) === null || _dailyChecks_ === void 0 ? void 0 : (_dailyChecks__customisedComponentFields = _dailyChecks_.customisedComponentFields) === null || _dailyChecks__customisedComponentFields === void 0 ? void 0 : _dailyChecks__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const convertTimeFormat = (time)=>{\n        if (time === null || time === undefined) return \"\";\n        const [hours, minutes] = time.split(\":\");\n        return \"\".concat(hours, \":\").concat(minutes);\n    };\n    const handleCancel = ()=>{\n        setOpenTripModal(false);\n        setCurrentTrip(false);\n        setAccordionValue(\"\");\n    };\n    // Removed unused functions\n    const initOffline = async ()=>{\n        var _localStorage_getItem;\n        // getOneClient\n        const client = await clientModel.getById((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0);\n        setClient(client);\n        // getVesselByID(+vesselID, setVessel)\n        const vessel = await vesselModel.getById(vesselID);\n        setVessel(vessel);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            initOffline();\n        }\n    }, [\n        offline\n    ]);\n    // Create combined trip data that merges tripReport and tripReportSchedules\n    const combinedTripData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const existingTrips = (tripReport || []).filter((trip)=>!(trip === null || trip === void 0 ? void 0 : trip.archived)).map((trip)=>{\n            var _trip_fromLocation, _trip_fromLocation1, _trip_toLocation, _trip_toLocation1, _trip_fromLocation2, _trip_toLocation2;\n            return {\n                ...trip,\n                isScheduled: false,\n                isCreated: true,\n                sortTime: trip.departTime || \"00:00:00\",\n                displayText: \"\".concat((trip === null || trip === void 0 ? void 0 : trip.departTime) ? convertTimeFormat(trip === null || trip === void 0 ? void 0 : trip.departTime) + \" - \" : \"No depart time - \").concat((trip === null || trip === void 0 ? void 0 : (_trip_fromLocation = trip.fromLocation) === null || _trip_fromLocation === void 0 ? void 0 : _trip_fromLocation.title) || \"\").concat((trip === null || trip === void 0 ? void 0 : (_trip_fromLocation1 = trip.fromLocation) === null || _trip_fromLocation1 === void 0 ? void 0 : _trip_fromLocation1.title) && (trip === null || trip === void 0 ? void 0 : (_trip_toLocation = trip.toLocation) === null || _trip_toLocation === void 0 ? void 0 : _trip_toLocation.title) ? \" -> \" : \"\").concat((trip === null || trip === void 0 ? void 0 : trip.arrive) ? convertTimeFormat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(trip === null || trip === void 0 ? void 0 : trip.arrive).format(\"HH:mm \")) : (trip === null || trip === void 0 ? void 0 : trip.arriveTime) ? convertTimeFormat(trip === null || trip === void 0 ? void 0 : trip.arriveTime) + \" - \" : \"- No arrival time \").concat((trip === null || trip === void 0 ? void 0 : (_trip_toLocation1 = trip.toLocation) === null || _trip_toLocation1 === void 0 ? void 0 : _trip_toLocation1.title) || \"\").concat(!(trip === null || trip === void 0 ? void 0 : (_trip_fromLocation2 = trip.fromLocation) === null || _trip_fromLocation2 === void 0 ? void 0 : _trip_fromLocation2.title) && !(trip === null || trip === void 0 ? void 0 : (_trip_toLocation2 = trip.toLocation) === null || _trip_toLocation2 === void 0 ? void 0 : _trip_toLocation2.title) ? \" - \" : \" \")\n            };\n        });\n        const scheduledTrips = (tripReportSchedules || []).map((schedule)=>{\n            // Check if this schedule has already been created as a trip\n            const isAlreadyCreated = existingTrips.some((trip)=>trip.tripReportScheduleID === schedule.id);\n            return {\n                ...schedule,\n                isScheduled: true,\n                isCreated: isAlreadyCreated,\n                sortTime: schedule.departTime || \"00:00:00\",\n                displayText: \"\".concat(schedule.departTime, \" - \").concat(schedule.arriveTime, \" | \").concat(schedule.fromLocation.title, \" → \").concat(schedule.toLocation.title)\n            };\n        });\n        // Combine and sort by departure time\n        const combined = [\n            ...existingTrips,\n            ...scheduledTrips.filter((s)=>!s.isCreated)\n        ];\n        return combined.sort((a, b)=>{\n            const timeA = a.sortTime || \"00:00:00\";\n            const timeB = b.sortTime || \"00:00:00\";\n            return timeA.localeCompare(timeB);\n        });\n    }, [\n        tripReport,\n        tripReportSchedules\n    ]);\n    // Handle creating a trip from a scheduled item\n    const handleCreateFromSchedule = (scheduleItem)=>{\n        setSelectedTripReportSchedule(scheduleItem);\n        const input = {\n            tripReportScheduleID: scheduleItem.id,\n            departTime: scheduleItem.departTime,\n            arriveTime: scheduleItem.arriveTime,\n            fromLocationID: scheduleItem.fromLocationID,\n            toLocationID: scheduleItem.toLocationID,\n            logBookEntryID: logentryID\n        };\n        doCreateTripReport(input, true) // true indicates this is a scheduled trip\n        ;\n    };\n    // Implement confirmDeletetrip using  AlertNew component to confirm trip deletion\n    const confirmDeleteTrip = (item)=>{\n        if (item) {\n            setDeleteTripItem(item);\n            setDeleteTripConfirmation(true);\n        }\n    };\n    const [deleteTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.DeleteTripReport_LogBookEntrySections, {\n        onCompleted: ()=>{\n            // const data = response.deleteTripReport_LogBookEntrySection\n            // updateTripReport({\n            //     id: data,\n            //     key: 'archived',\n            //     value: true,\n            // })\n            updateTripReport({\n                id: [\n                    ...tripReport.filter((trip)=>trip.id !== deleteTripItem.id).map((trip)=>trip.id)\n                ]\n            });\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error deleting trip report\", error);\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        }\n    });\n    const handleDeleteTrip = async ()=>{\n        if (deleteTripItem) {\n            if (!offline) {\n                await deleteTripReport_LogBookEntrySection({\n                    variables: {\n                        ids: [\n                            deleteTripItem.id\n                        ]\n                    }\n                });\n            } else {\n                await tripReportModel.save({\n                    id: deleteTripItem.id,\n                    archived: true\n                });\n            }\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex xs:flex-row flex-col xs:justify-between xs:gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__.Combobox, {\n                            options: tripScheduleServices,\n                            value: tripScheduleServices.find((option)=>option.value === selectedTripScheduleServiceID) || null,\n                            onChange: (e)=>{\n                                if (e) {\n                                    setSelectedTripScheduleServiceID(e.value);\n                                    loadTripReportSchedules(e.value);\n                                } else {\n                                    setSelectedTripScheduleServiceID(null);\n                                    setTripReportSchedules([]);\n                                    setShowNextTrips(false);\n                                }\n                            },\n                            placeholder: \"Select Trip Schedule Service\",\n                            disabled: locked\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1196,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                            onClick: handleCustomTrip,\n                            variant: \"primary\",\n                            disabled: locked,\n                            children: \"Add Non-Scheduled Trip\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1218,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 1195,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1194,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: \"This section covers the logbook entry. This can be made up of a single trip or many over the course of the voyage.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1249,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: combinedTripData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_22__.Accordion, {\n                    type: \"single\",\n                    collapsible: true,\n                    value: accordionValue,\n                    onValueChange: (value)=>{\n                        setAccordionValue(value);\n                        // If we're closing the accordion, reset the state\n                        if (value === \"\") {\n                            setSelectedTab(0);\n                            setOpenTripModal(false);\n                            setCurrentTrip(false);\n                            setSelectedTripReportSchedule(null);\n                        } else {\n                            // Find the selected item from combined data\n                            const selectedItem = combinedTripData.find((item)=>item.id.toString() === value);\n                            if (selectedItem) {\n                                if (selectedItem.isScheduled && !selectedItem.isCreated) {\n                                    // This is a scheduled trip that hasn't been created yet\n                                    // Don't set currentTrip, just expand the accordion\n                                    setSelectedTab(0);\n                                    setOpenTripModal(false);\n                                    setCurrentTrip(false);\n                                    setSelectedTripReportSchedule(null);\n                                } else {\n                                    // This is an existing trip or a created scheduled trip\n                                    setSelectedTab(selectedItem.id);\n                                    setOpenTripModal(true);\n                                    setSignatureKey(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default()());\n                                    currentTripRef.current = selectedItem;\n                                    setCurrentTrip(selectedItem);\n                                    setSelectedTripReportSchedule(null);\n                                    // Initialize signature data if available\n                                    if (selectedItem.sectionSignature) {\n                                        setSignature(selectedItem.sectionSignature.signatureData || \"\");\n                                    } else {\n                                        setSignature(\"\");\n                                    }\n                                    setRiskBufferEvDgr(selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.dangerousGoodsChecklist);\n                                    setOpenTripStartRiskAnalysis(false);\n                                    setAllDangerousGoods(false);\n                                    setCurrentStopEvent(false);\n                                    setCurrentEventTypeEvent(false);\n                                    setSelectedRowEvent(false);\n                                    setDisplayDangerousGoods((selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.enableDGR) === true);\n                                    setDisplayDangerousGoodsSailing((selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.designatedDangerousGoodsSailing) === true);\n                                    setDisplayDangerousGoodsPvpd(false);\n                                    setDisplayDangerousGoodsPvpdSailing(null);\n                                    setAllPVPDDangerousGoods(false);\n                                    setSelectedDGRPVPD(false);\n                                    setTripReport_Stops(false);\n                                }\n                            }\n                        }\n                    },\n                    children: combinedTripData.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_22__.AccordionItem, {\n                            value: item.id.toString(),\n                            id: \"combined-trip-\".concat(item.id),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_22__.AccordionTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.displayText\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1338,\n                                                        columnNumber: 45\n                                                    }, this),\n                                                    item.isScheduled && !item.isCreated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\",\n                                                        children: \"Scheduled\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1341,\n                                                        columnNumber: 53\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1337,\n                                                columnNumber: 41\n                                            }, this),\n                                            item.isScheduled && !item.isCreated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleCreateFromSchedule(item);\n                                                },\n                                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"],\n                                                size: \"sm\",\n                                                className: \"ml-2\",\n                                                disabled: locked,\n                                                children: \"Create Trip\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1348,\n                                                columnNumber: 49\n                                            }, this),\n                                            !item.isScheduled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    confirmDeleteTrip(item);\n                                                },\n                                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_43__[\"default\"],\n                                                size: \"sm\",\n                                                className: \"ml-2\",\n                                                variant: \"destructive\",\n                                                disabled: locked,\n                                                children: \"Delete Trip\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1363,\n                                                columnNumber: 45\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 1336,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                    lineNumber: 1335,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_22__.AccordionContent, {\n                                    className: \"px-5 sm:px-10\",\n                                    children: !item.isScheduled || item.isCreated ? currentTrip && currentTrip.id === item.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TripReportAccordionContent, {\n                                        currentTrip: currentTrip,\n                                        offline: offline,\n                                        tripReport: tripReport,\n                                        updateTripReport: updateTripReport,\n                                        updateTripReport_LogBookEntrySection: updateTripReport_LogBookEntrySection,\n                                        currentTripRef: currentTripRef,\n                                        locations: locations,\n                                        locked: locked,\n                                        edit_tripReport: edit_tripReport,\n                                        vessel: vessel,\n                                        crewMembers: crewMembers,\n                                        logBookConfig: logBookConfig,\n                                        client: client,\n                                        canCarryVehicles: canCarryVehicles,\n                                        canCarryDangerousGoods: canCarryDangerousGoods,\n                                        selectedDGR: selectedDGR,\n                                        displayDangerousGoods: displayDangerousGoods,\n                                        setDisplayDangerousGoods: setDisplayDangerousGoods,\n                                        displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                                        setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                                        allDangerousGoods: allDangerousGoods,\n                                        setAllDangerousGoods: setAllDangerousGoods,\n                                        logBookStartDate: logBookStartDate,\n                                        masterID: masterID,\n                                        vessels: vessels,\n                                        setSelectedRowEvent: setSelectedRowEvent,\n                                        setCurrentEventTypeEvent: setCurrentEventTypeEvent,\n                                        setCurrentStopEvent: setCurrentStopEvent,\n                                        currentEventTypeEvent: currentEventTypeEvent,\n                                        currentStopEvent: currentStopEvent,\n                                        tripReport_Stops: tripReport_Stops,\n                                        setTripReport_Stops: setTripReport_Stops,\n                                        displayDangerousGoodsPvpd: displayDangerousGoodsPvpd,\n                                        setDisplayDangerousGoodsPvpd: setDisplayDangerousGoodsPvpd,\n                                        displayDangerousGoodsPvpdSailing: displayDangerousGoodsPvpdSailing,\n                                        setDisplayDangerousGoodsPvpdSailing: setDisplayDangerousGoodsPvpdSailing,\n                                        allPVPDDangerousGoods: allPVPDDangerousGoods,\n                                        setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                                        selectedDGRPVPD: selectedDGRPVPD,\n                                        setSelectedDGRPVPD: setSelectedDGRPVPD,\n                                        fuelLogs: fuelLogs,\n                                        comment: comment,\n                                        setComment: setComment,\n                                        displayFieldTripLog: displayFieldTripLog,\n                                        signatureKey: signatureKey,\n                                        signature: signature,\n                                        setSignature: setSignature,\n                                        handleCancel: handleCancel,\n                                        handleSave: handleSave\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 1383,\n                                        columnNumber: 45\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-muted-foreground\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: 'Click the \"Create Trip\" button above to create this scheduled trip and access the trip details.'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 1493,\n                                            columnNumber: 45\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 1492,\n                                        columnNumber: 41\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                    lineNumber: 1378,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, \"combined-trip-\".concat(item.id, \"-\").concat(item.isScheduled ? \"scheduled\" : \"existing\"), true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1331,\n                            columnNumber: 29\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 1256,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1254,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_23__.AlertDialogNew, {\n                openDialog: deleteTripConfirmation,\n                setOpenDialog: setDeleteTripConfirmation,\n                // handleCreate={handleDeleteTrip}\n                title: \"Delete Trip\",\n                description: \"Are you sure you want to delete this trip? This action cannot be undone.\",\n                cancelText: \"Cancel\",\n                destructiveActionText: \"Delete\",\n                handleDestructiveAction: handleDeleteTrip,\n                showDestructiveAction: true,\n                variant: \"danger\",\n                actionText: \"Delete\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1508,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(TripLog, \"nlN3gY5V9iZg3D5RIXsBR/+0MBA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__.useToast,\n        nuqs__WEBPACK_IMPORTED_MODULE_39__.useQueryState,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation\n    ];\n});\n_c1 = TripLog;\nvar _c, _c1;\n$RefreshReg$(_c, \"TripReportAccordionContent\");\n$RefreshReg$(_c1, \"TripLog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx\n"));

/***/ })

});