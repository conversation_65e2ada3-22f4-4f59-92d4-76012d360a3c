"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx":
/*!*****************************************!*\
  !*** ./src/app/ui/logbook/trip-log.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TripLog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash/uniqueId */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqueId.js\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/client */ \"(app-pages-browser)/./src/app/offline/models/client.js\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_eventType__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/models/eventType */ \"(app-pages-browser)/./src/app/offline/models/eventType.js\");\n/* harmony import */ var _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/offline/models/tripReport_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/tripReport_LogBookEntrySection.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_accordion__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/accordion */ \"(app-pages-browser)/./src/components/ui/accordion.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _depart_time__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./depart-time */ \"(app-pages-browser)/./src/app/ui/logbook/depart-time.tsx\");\n/* harmony import */ var _exp_arrival_time__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./exp-arrival-time */ \"(app-pages-browser)/./src/app/ui/logbook/exp-arrival-time.tsx\");\n/* harmony import */ var _actual_arrival_time__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./actual-arrival-time */ \"(app-pages-browser)/./src/app/ui/logbook/actual-arrival-time.tsx\");\n/* harmony import */ var _events__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./events */ \"(app-pages-browser)/./src/app/ui/logbook/events.tsx\");\n/* harmony import */ var _trip_log_pob__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./trip-log-pob */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-pob.tsx\");\n/* harmony import */ var _trip_log_vob__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./trip-log-vob */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-vob.tsx\");\n/* harmony import */ var _trip_log_dgr__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./trip-log-dgr */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-dgr.tsx\");\n/* harmony import */ var _components_trip_comments__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./components/trip-comments */ \"(app-pages-browser)/./src/app/ui/logbook/components/trip-comments.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_master__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! ./components/master */ \"(app-pages-browser)/./src/app/ui/logbook/components/master.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// React and Next.js imports\n\n\n\n\n\n\n// Utility imports\n\n\n\n\n\n\n// Model imports\n\n\n\n\n\n// UI Component imports\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Reusable TripReportAccordionContent component\nconst TripReportAccordionContent = (param)=>{\n    let { currentTrip, offline, tripReport, updateTripReport, updateTripReport_LogBookEntrySection, currentTripRef, locations, locked, edit_tripReport, vessel, crewMembers, logBookConfig, client, canCarryVehicles, canCarryDangerousGoods, selectedDGR, displayDangerousGoods, setDisplayDangerousGoods, displayDangerousGoodsSailing, setDisplayDangerousGoodsSailing, allDangerousGoods, setAllDangerousGoods, logBookStartDate, masterID, vessels, setSelectedRowEvent, setCurrentEventTypeEvent, setCurrentStopEvent, currentEventTypeEvent, currentStopEvent, tripReport_Stops, setTripReport_Stops, displayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpd, displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpdSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGRPVPD, setSelectedDGRPVPD, fuelLogs, comment, setComment, displayFieldTripLog, signatureKey, signature, setSignature, handleCancel, handleSave } = param;\n    var _currentTripRef_current;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_34__.cn)(\"space-y-6\", locked || !edit_tripReport ? \"opacity-70 pointer-events-none\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col inset-y-0 items-center pt-10 pb-4 absolute -left-5 sm:-left-[25px] min-h-24 w-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_34__.cn)(\"size-[11px] z-10 rounded-full\", \"border border-neutral-400 bg-background\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_34__.cn)(\"border-l h-full border-wedgewood-200 border-dashed\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_34__.cn)(\"size-[11px] z-10 rounded-full\", \"border border-neutral-400 bg-background\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_depart_time__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        offline: offline,\n                        currentTrip: currentTrip,\n                        tripReport: tripReport,\n                        templateStyle: \"\",\n                        updateTripReport: updateTripReport\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_21__.Label, {\n                        label: \"Departure location\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            offline: offline,\n                            setCurrentLocation: (location)=>{\n                            // Store coordinates if needed for direct coordinate input\n                            },\n                            handleLocationChange: (selectedLocation)=>{\n                                // Update the from location\n                                if (offline) {\n                                    updateTripReport({\n                                        id: [\n                                            ...tripReport.map((trip)=>trip.id),\n                                            currentTrip.id\n                                        ],\n                                        currentTripID: currentTrip.id,\n                                        key: \"fromLocationID\",\n                                        value: selectedLocation.value,\n                                        label: selectedLocation.label\n                                    });\n                                } else {\n                                    var _currentTripRef_current;\n                                    // For online mode, use the mutation\n                                    updateTripReport_LogBookEntrySection({\n                                        variables: {\n                                            input: {\n                                                id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                                                fromLocationID: selectedLocation.value\n                                            }\n                                        }\n                                    });\n                                }\n                            },\n                            currentEvent: {\n                                geoLocationID: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLocationID) || 0,\n                                lat: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLat) || 0,\n                                long: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLong) || 0\n                            },\n                            showAddNewLocation: true,\n                            showUseCoordinates: true,\n                            showCurrentLocation: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_20__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.H5, {\n                        children: \"PEOPLE ON BOARD\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_pob__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                        offline: offline,\n                        currentTrip: currentTrip,\n                        tripReport: tripReport,\n                        vessel: vessel,\n                        crewMembers: crewMembers,\n                        logBookConfig: logBookConfig,\n                        masterTerm: client === null || client === void 0 ? void 0 : client.masterTerm,\n                        updateTripReport: updateTripReport\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_20__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            canCarryVehicles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_vob__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                offline: offline,\n                                currentTrip: currentTrip,\n                                logBookConfig: logBookConfig\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 25\n                            }, undefined),\n                            canCarryDangerousGoods && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_dgr__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                offline: offline,\n                                locked: locked || !edit_tripReport,\n                                currentTrip: currentTrip,\n                                logBookConfig: logBookConfig,\n                                selectedDGR: selectedDGR,\n                                members: crewMembers,\n                                displayDangerousGoods: displayDangerousGoods,\n                                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                                allDangerousGoods: allDangerousGoods,\n                                setAllDangerousGoods: setAllDangerousGoods\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_20__.Separator, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_events__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        offline: offline,\n                        logBookStartDate: logBookStartDate,\n                        currentTrip: currentTrip,\n                        logBookConfig: logBookConfig,\n                        updateTripReport: updateTripReport,\n                        locked: locked,\n                        geoLocations: locations,\n                        tripReport: tripReport,\n                        crewMembers: crewMembers,\n                        masterID: masterID,\n                        vessel: vessel,\n                        vessels: vessels,\n                        setSelectedRow: setSelectedRowEvent,\n                        setCurrentEventType: setCurrentEventTypeEvent,\n                        setCurrentStop: setCurrentStopEvent,\n                        currentEventType: currentEventTypeEvent,\n                        currentStop: currentStopEvent,\n                        tripReport_Stops: tripReport_Stops,\n                        setTripReport_Stops: setTripReport_Stops,\n                        displayDangerousGoodsPvpd: displayDangerousGoodsPvpd,\n                        setDisplayDangerousGoodsPvpd: setDisplayDangerousGoodsPvpd,\n                        displayDangerousGoodsPvpdSailing: displayDangerousGoodsPvpdSailing,\n                        setDisplayDangerousGoodsPvpdSailing: setDisplayDangerousGoodsPvpdSailing,\n                        allPVPDDangerousGoods: allPVPDDangerousGoods,\n                        setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                        selectedDGRPVPD: selectedDGRPVPD,\n                        setSelectedDGRPVPD: setSelectedDGRPVPD,\n                        fuelLogs: fuelLogs\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_20__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_21__.Label, {\n                        label: \"Arrival location\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            offline: offline,\n                            setCurrentLocation: (location)=>{\n                            // Store coordinates if needed for direct coordinate input\n                            },\n                            handleLocationChange: (selectedLoc)=>{\n                                // Update the to location\n                                if (offline) {\n                                    updateTripReport({\n                                        id: [\n                                            ...tripReport.map((trip)=>trip.id),\n                                            currentTrip.id\n                                        ],\n                                        currentTripID: currentTrip.id,\n                                        key: \"toLocationID\",\n                                        value: selectedLoc.value,\n                                        label: selectedLoc.label\n                                    });\n                                } else {\n                                    var _currentTripRef_current;\n                                    // For online mode, use the mutation\n                                    updateTripReport_LogBookEntrySection({\n                                        variables: {\n                                            input: {\n                                                id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                                                toLocationID: selectedLoc.value\n                                            }\n                                        }\n                                    });\n                                }\n                            },\n                            currentEvent: {\n                                geoLocationID: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLocationID) || 0,\n                                lat: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLat) || 0,\n                                long: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLong) || 0\n                            },\n                            showAddNewLocation: true,\n                            showUseCoordinates: true,\n                            showCurrentLocation: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 130,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_exp_arrival_time__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 339,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_actual_arrival_time__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 346,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_trip_comments__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                setCommentField: setComment,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 353,\n                columnNumber: 13\n            }, undefined),\n            displayFieldTripLog(\"MasterID\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked || !edit_tripReport ? \"pointer-events-none\" : \"\", \" max-w-sm\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_master__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                    offline: offline,\n                    currentTrip: currentTrip,\n                    tripReport: tripReport,\n                    crewMembers: crewMembers,\n                    updateTripReport: updateTripReport\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 361,\n                columnNumber: 17\n            }, undefined),\n            displayFieldTripLog(\"Signature\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                locked: locked,\n                title: \"Signature Confirmation\",\n                description: \"By signing below, I confirm that the recorded entries are accurate to the best of my knowledge and in accordance with the vessel's operating procedures and regulations.\",\n                signature: currentTripRef === null || currentTripRef === void 0 ? void 0 : (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.sectionSignature,\n                onSignatureChanged: (sign)=>{\n                    setSignature(sign);\n                }\n            }, \"\".concat(signatureKey, \"-\").concat(currentTrip.id), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 374,\n                columnNumber: 17\n            }, undefined),\n            !locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_35__.FormFooter, {\n                className: \"justify-end gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"],\n                        onClick: handleCancel,\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"],\n                        onClick: handleSave,\n                        children: \"Update\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 387,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n        lineNumber: 123,\n        columnNumber: 9\n    }, undefined);\n};\n_c = TripReportAccordionContent;\nfunction TripLog(param) {\n    let { tripReport = false, logBookConfig, updateTripReport, locked, crewMembers, masterID, createdTab = false, setCreatedTab, currentTrip = false, setCurrentTrip, vessels, offline = false, fuelLogs, logBookStartDate } = param;\n    var _tripReport_find;\n    _s();\n    // const router = useRouter()\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    var _searchParams_get1;\n    const vesselID = (_searchParams_get1 = searchParams.get(\"vesselID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__.useToast)();\n    // Use nuqs to manage the tab state through URL query parameters\n    const [tab, setTab] = (0,nuqs__WEBPACK_IMPORTED_MODULE_39__.useQueryState)(\"tab\", {\n        defaultValue: \"crew\"\n    });\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [eventTypes, setEventTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openTripModal, setOpenTripModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [bufferTripID, setBufferTripID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [vessel, setVessel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [accordionValue, setAccordionValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedDGR, setSelectedDGR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [openRiskAnalysis, setOpenTripStartRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoods, setDisplayDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsSailing, setDisplayDangerousGoodsSailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpdSailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // const [openEventModal, setOpenEventModal] = useState(false)\n    // const [selectedRowdgr, setSelectedRowdgr] = useState<any>(false)\n    const [tripReport_Stops, setTripReport_Stops] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDGRPVPD, setSelectedDGRPVPD] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [allPVPDDangerousGoods, setAllPVPDDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRowEvent, setSelectedRowEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [riskBufferEvDgr, setRiskBufferEvDgr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allDangerousGoods, setAllDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentEventTypeEvent, setCurrentEventTypeEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStopEvent, setCurrentStopEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [signature, setSignature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [signatureKey, setSignatureKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default()());\n    const currentTripRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [deleteTripItem, setDeleteTripItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteTripConfirmation, setDeleteTripConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const canCarryDangerousGoods = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _vessel_vesselSpecifics;\n        return !!(vessel === null || vessel === void 0 ? void 0 : (_vessel_vesselSpecifics = vessel.vesselSpecifics) === null || _vessel_vesselSpecifics === void 0 ? void 0 : _vessel_vesselSpecifics.carriesDangerousGoods);\n    }, [\n        vessel\n    ]);\n    const canCarryVehicles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _vessel_vesselSpecifics;\n        return !!(vessel === null || vessel === void 0 ? void 0 : (_vessel_vesselSpecifics = vessel.vesselSpecifics) === null || _vessel_vesselSpecifics === void 0 ? void 0 : _vessel_vesselSpecifics.carriesVehicles);\n    }, [\n        vessel\n    ]);\n    // Initialize client\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getOneClient)(setClient);\n    }\n    // Update signature state when currentTrip changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentTrip && currentTrip.sectionSignature) {\n            setSignature(currentTrip.sectionSignature.signatureData || \"\");\n        } else {\n            setSignature(\"\");\n        }\n    }, [\n        currentTrip\n    ]);\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(tripReport ? tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find === void 0 ? void 0 : _tripReport_find.comment : \"\");\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_tripReport, setEdit_tripReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const clientModel = new _app_offline_models_client__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const eventTypeModel = new _app_offline_models_eventType__WEBPACK_IMPORTED_MODULE_14__[\"default\"]();\n    const tripReportModel = new _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__[\"default\"]();\n    const [openTripSelectionDialog, setOpenTripSelectionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tripReportSchedules, setTripReportSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTripReportSchedule, setSelectedTripReportSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tripScheduleServices, setTripScheduleServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTripScheduleServiceID, setSelectedTripScheduleServiceID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showNextTrips, setShowNextTrips] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreatingScheduledTrip, setIsCreatingScheduledTrip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"EDIT_LOGBOOKENTRY_TRIPREPORT\", permissions)) {\n                setEdit_tripReport(true);\n            } else {\n                setEdit_tripReport(false);\n            }\n        }\n    };\n    const offlineLoad = async ()=>{\n        const locations = await geoLocationModel.getAll();\n        setLocations(locations);\n        const types = await eventTypeModel.getAll();\n        setEventTypes(types);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.getPermissions);\n        loadTripScheduleServices();\n        if (!locations) {\n            if (offline) {\n                offlineLoad();\n            } else {\n                loadLocations();\n                loadEventTypes();\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (createdTab) {\n            setSelectedTab(createdTab);\n        }\n    }, [\n        createdTab\n    ]);\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getVesselByID)(+vesselID, setVessel);\n    }\n    const scrollToAccordionItem = (tripId)=>{\n        const element = document.getElementById(\"triplog-\".concat(tripId));\n        if (element) {\n            element.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (tripReport && currentTrip) {\n            const trip = tripReport.find((trip)=>trip.id === currentTrip.id);\n            currentTripRef.current = trip;\n            setCurrentTrip(trip);\n        }\n        if (tripReport && bufferTripID > 0) {\n            const trip = tripReport.find((trip)=>trip.id === bufferTripID);\n            if (trip) {\n                currentTripRef.current = trip;\n                setCurrentTrip(trip);\n                // Only expand accordion and scroll for regular trips, not scheduled trips\n                if (!selectedTripReportSchedule) {\n                    setAccordionValue(trip.id.toString());\n                    scrollToAccordionItem(trip.id);\n                }\n                setOpenTripModal(true);\n                setSelectedTab(trip.id);\n                setSignatureKey(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default()());\n                // Initialize signature data if available\n                if (trip.sectionSignature) {\n                    setSignature(trip.sectionSignature.signatureData || \"\");\n                } else {\n                    setSignature(\"\");\n                }\n                // Initialize trip-specific state\n                setRiskBufferEvDgr(trip === null || trip === void 0 ? void 0 : trip.dangerousGoodsChecklist);\n                setOpenTripStartRiskAnalysis(false);\n                setAllDangerousGoods(false);\n                setCurrentStopEvent(false);\n                setCurrentEventTypeEvent(false);\n                setSelectedRowEvent(false);\n                setDisplayDangerousGoods((trip === null || trip === void 0 ? void 0 : trip.enableDGR) === true);\n                setDisplayDangerousGoodsSailing((trip === null || trip === void 0 ? void 0 : trip.designatedDangerousGoodsSailing) === true);\n                setDisplayDangerousGoodsPvpd(false);\n                setDisplayDangerousGoodsPvpdSailing(null);\n                setAllPVPDDangerousGoods(false);\n                setSelectedDGRPVPD(false);\n                setTripReport_Stops(false);\n            }\n            setBufferTripID(0);\n        }\n    }, [\n        tripReport\n    ]);\n    const [loadLocations] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_GEO_LOCATIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setLocations(response.readGeoLocations.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading locations\", error);\n        }\n    });\n    const [loadEventTypes] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_EVENT_TYPES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setEventTypes(response.readEventTypes.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading activity types\", error);\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripReport_Stop, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const handleCreateTripReportScheduleStops = async (logBookEntrySectionID)=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(selectedTripReportSchedule)) {\n            const tripStops = selectedTripReportSchedule.tripReportScheduleStops.nodes || [];\n            await Promise.all(tripStops.map(async (stop)=>{\n                const input = {\n                    logBookEntrySectionID: logBookEntrySectionID,\n                    tripReportScheduleStopID: stop.id,\n                    arriveTime: stop.arriveTime,\n                    departTime: stop.departTime,\n                    stopLocationID: stop.stopLocationID\n                };\n                await createTripReport_Stop({\n                    variables: {\n                        input: input\n                    }\n                });\n            }));\n            setSelectedTripReportSchedule(null);\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        logBookEntrySectionID\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        logBookEntrySectionID\n                    ]\n                });\n            }\n        }\n    };\n    const [createTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripReport_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_LogBookEntrySection;\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ]\n                });\n            }\n            setCreatedTab(data.id);\n            setBufferTripID(data.id);\n            handleCreateTripReportScheduleStops(data.id);\n            // Always set accordion value and scroll for newly created trips\n            setAccordionValue(data.id.toString());\n            scrollToAccordionItem(data.id);\n            // Reset the flag\n            setIsCreatingScheduledTrip(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip report\", error);\n        }\n    });\n    const [updateTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateTripReport_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_LogBookEntrySection;\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            }\n            setBufferTripID(data.id);\n            setCurrentTrip(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip report\", error);\n        }\n    });\n    const [readTripReportSchedules, { loading: readTripReportSchedulesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.ReadTripReportSchedules, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripReportSchedules.nodes.filter((trip)=>// only show trips for the current vessel\n                trip.vehicles.nodes.some((vehicle)=>+vehicle.id === +vesselID));\n            if (showNextTrips) {\n                // only show 1 past trip and 4 upcoming trips\n                const currentTime = dayjs__WEBPACK_IMPORTED_MODULE_5___default()().format(\"HH:mm:ss\");\n                const pastIndex = data.findIndex((trip)=>trip.departTime >= currentTime);\n                const result = (pastIndex > 0 ? [\n                    data[pastIndex - 1]\n                ] : []).concat(data.slice(pastIndex, pastIndex + 4));\n                setTripReportSchedules(result);\n            } else {\n                setTripReportSchedules(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error loading TripReportSchedules\", error);\n        }\n    });\n    const [readTripScheduleServices] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.ReadTripScheduleServices, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripScheduleServices.nodes.map((tss)=>{\n                return {\n                    label: tss.title,\n                    value: tss.id\n                };\n            });\n            setTripScheduleServices(data);\n            setTripReportSchedules([]);\n        // setOpenTripSelectionDialog(true)\n        },\n        onError: (error)=>{\n            console.error(\"Error loading TripScheduleServices\", error);\n        }\n    });\n    const loadTripScheduleServices = async ()=>{\n        await readTripScheduleServices({\n            variables: {\n                filter: {\n                    vehicles: {\n                        id: {\n                            eq: vesselID\n                        }\n                    }\n                }\n            }\n        });\n    };\n    const loadTripReportSchedules = async (tripScheduleServiceID)=>{\n        setTripReportSchedules([]);\n        await readTripReportSchedules({\n            variables: {\n                filter: {\n                    // archived: { eq: false },\n                    // start: { eq: logBookStartDate },\n                    tripScheduleServiceID: {\n                        eq: tripScheduleServiceID\n                    }\n                }\n            }\n        });\n    };\n    const doCreateTripReport = async function(input) {\n        let isScheduledTrip = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!edit_tripReport) {\n            toast({\n                title: \"Error\",\n                description: \"You do not have permission to add a trip\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setOpenTripModal(false);\n        setCurrentTrip(false);\n        setIsCreatingScheduledTrip(isScheduledTrip);\n        if (offline) {\n            const data = await tripReportModel.save({\n                ...input,\n                id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)()\n            });\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ]\n                });\n            }\n            setCreatedTab(data.id);\n            setBufferTripID(data.id);\n            // For offline mode, immediately set accordion value since the trip is created synchronously\n            setAccordionValue(data.id.toString());\n            // Reset the flag\n            setIsCreatingScheduledTrip(false);\n        } else {\n            createTripReport_LogBookEntrySection({\n                variables: {\n                    input: input\n                }\n            });\n        }\n        setRiskBufferEvDgr(false);\n        setOpenTripStartRiskAnalysis(false);\n        setAllDangerousGoods(false);\n        setCurrentStopEvent(false);\n        setCurrentEventTypeEvent(false);\n        setSelectedRowEvent(false);\n        setDisplayDangerousGoods(false);\n        setDisplayDangerousGoodsSailing(false);\n        setDisplayDangerousGoodsPvpd(false);\n        setDisplayDangerousGoodsPvpdSailing(null);\n        setAllPVPDDangerousGoods(false);\n        setSelectedDGRPVPD(false);\n        setTripReport_Stops(false);\n    // setSelectedTripScheduleServiceID(null)\n    // setTripReportSchedules([])\n    // setShowNextTrips(false)\n    };\n    const handleAddTrip = async ()=>{\n        const allowedVesselTypes = [\n            \"SLALL\",\n            \"Tug_Boat\",\n            \"Passenger_Ferry\",\n            \"Water_Taxi\"\n        ];\n        if (allowedVesselTypes.includes(vessel.vesselType)) {\n            loadTripScheduleServices();\n        } else {\n            handleCustomTrip();\n        }\n    };\n    const handleCustomTrip = ()=>{\n        setOpenTripSelectionDialog(false);\n        const input = {\n            logBookEntryID: logentryID\n        };\n        doCreateTripReport(input, false) // false indicates this is a regular trip\n        ;\n    };\n    // Removed unused handleEditTrip function\n    const [createLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateLogBookEntrySection_Signature, {\n        onCompleted: (response)=>{\n            const data = response.createLogBookEntrySection_Signature;\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: +data.logBookEntrySectionID,\n                        sectionSignatureID: +(data === null || data === void 0 ? void 0 : data.id)\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error saving signature\", error);\n        }\n    });\n    const [updateLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateLogBookEntrySection_Signature, {\n        onCompleted: (response)=>{\n            var _currentTripRef_current;\n            const data = response.updateLogBookEntrySection_Signature;\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                        sectionSignatureID: +(data === null || data === void 0 ? void 0 : data.id)\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"341 TripLog updateLogBookEntrySection_Signature\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        // Log signature information for debugging\n        // Ensure we have a valid signature\n        const sigVariables = {\n            logBookEntrySectionID: currentTrip.id,\n            memberID: localStorage.getItem(\"userId\"),\n            signatureData: signature || \"\"\n        };\n        if (+currentTrip.sectionSignatureID > 0) {\n            // Update signature\n            updateLogBookEntrySection_Signature({\n                variables: {\n                    input: {\n                        ...sigVariables,\n                        id: +currentTrip.sectionSignatureID\n                    }\n                }\n            });\n        } else {\n            // Create signature\n            createLogBookEntrySection_Signature({\n                variables: {\n                    input: sigVariables !== null && sigVariables !== void 0 ? sigVariables : \"\"\n                }\n            });\n        }\n        if (offline) {\n            // updateTripReport_LogBookEntrySection\n            const data = await tripReportModel.save({\n                id: currentTrip.id,\n                comment: comment || null\n            });\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            }\n            setBufferTripID(data.id);\n        } else {\n            var _currentTripRef_current;\n            await updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                        comment: comment || null\n                    }\n                }\n            });\n            setOpenTripModal(false);\n            setCurrentTrip(false);\n        }\n    };\n    const displayFieldTripLog = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _dailyChecks__customisedComponentFields, _dailyChecks_;\n        const dailyChecks = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"TripReport_LogBookComponent\");\n        if ((dailyChecks === null || dailyChecks === void 0 ? void 0 : dailyChecks.length) > 0 && ((_dailyChecks_ = dailyChecks[0]) === null || _dailyChecks_ === void 0 ? void 0 : (_dailyChecks__customisedComponentFields = _dailyChecks_.customisedComponentFields) === null || _dailyChecks__customisedComponentFields === void 0 ? void 0 : _dailyChecks__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const convertTimeFormat = (time)=>{\n        if (time === null || time === undefined) return \"\";\n        const [hours, minutes] = time.split(\":\");\n        return \"\".concat(hours, \":\").concat(minutes);\n    };\n    const handleCancel = ()=>{\n        setOpenTripModal(false);\n        setCurrentTrip(false);\n        setAccordionValue(\"\");\n    };\n    // Removed unused functions\n    const initOffline = async ()=>{\n        var _localStorage_getItem;\n        // getOneClient\n        const client = await clientModel.getById((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0);\n        setClient(client);\n        // getVesselByID(+vesselID, setVessel)\n        const vessel = await vesselModel.getById(vesselID);\n        setVessel(vessel);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            initOffline();\n        }\n    }, [\n        offline\n    ]);\n    // Create combined trip data that merges tripReport and tripReportSchedules\n    const combinedTripData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const existingTrips = (tripReport || []).filter((trip)=>!(trip === null || trip === void 0 ? void 0 : trip.archived)).map((trip)=>{\n            var _trip_fromLocation, _trip_fromLocation1, _trip_toLocation, _trip_toLocation1, _trip_fromLocation2, _trip_toLocation2;\n            return {\n                ...trip,\n                isScheduled: false,\n                isCreated: true,\n                sortTime: trip.departTime || \"00:00:00\",\n                displayText: \"\".concat((trip === null || trip === void 0 ? void 0 : trip.departTime) ? convertTimeFormat(trip === null || trip === void 0 ? void 0 : trip.departTime) + \" - \" : \"No depart time - \").concat((trip === null || trip === void 0 ? void 0 : (_trip_fromLocation = trip.fromLocation) === null || _trip_fromLocation === void 0 ? void 0 : _trip_fromLocation.title) || \"\").concat((trip === null || trip === void 0 ? void 0 : (_trip_fromLocation1 = trip.fromLocation) === null || _trip_fromLocation1 === void 0 ? void 0 : _trip_fromLocation1.title) && (trip === null || trip === void 0 ? void 0 : (_trip_toLocation = trip.toLocation) === null || _trip_toLocation === void 0 ? void 0 : _trip_toLocation.title) ? \" -> \" : \"\").concat((trip === null || trip === void 0 ? void 0 : trip.arrive) ? convertTimeFormat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(trip === null || trip === void 0 ? void 0 : trip.arrive).format(\"HH:mm \")) : (trip === null || trip === void 0 ? void 0 : trip.arriveTime) ? convertTimeFormat(trip === null || trip === void 0 ? void 0 : trip.arriveTime) + \" - \" : \"- No arrival time \").concat((trip === null || trip === void 0 ? void 0 : (_trip_toLocation1 = trip.toLocation) === null || _trip_toLocation1 === void 0 ? void 0 : _trip_toLocation1.title) || \"\").concat(!(trip === null || trip === void 0 ? void 0 : (_trip_fromLocation2 = trip.fromLocation) === null || _trip_fromLocation2 === void 0 ? void 0 : _trip_fromLocation2.title) && !(trip === null || trip === void 0 ? void 0 : (_trip_toLocation2 = trip.toLocation) === null || _trip_toLocation2 === void 0 ? void 0 : _trip_toLocation2.title) ? \" - \" : \" \")\n            };\n        });\n        const scheduledTrips = (tripReportSchedules || []).map((schedule)=>{\n            // Check if this schedule has already been created as a trip\n            const isAlreadyCreated = existingTrips.some((trip)=>trip.tripReportScheduleID === schedule.id);\n            return {\n                ...schedule,\n                isScheduled: true,\n                isCreated: isAlreadyCreated,\n                sortTime: schedule.departTime || \"00:00:00\",\n                displayText: \"\".concat(schedule.departTime, \" - \").concat(schedule.arriveTime, \" | \").concat(schedule.fromLocation.title, \" → \").concat(schedule.toLocation.title)\n            };\n        });\n        // Combine and sort by departure time\n        const combined = [\n            ...existingTrips,\n            ...scheduledTrips.filter((s)=>!s.isCreated)\n        ];\n        return combined.sort((a, b)=>{\n            const timeA = a.sortTime || \"00:00:00\";\n            const timeB = b.sortTime || \"00:00:00\";\n            return timeA.localeCompare(timeB);\n        });\n    }, [\n        tripReport,\n        tripReportSchedules\n    ]);\n    // Handle creating a trip from a scheduled item\n    const handleCreateFromSchedule = (scheduleItem)=>{\n        setSelectedTripReportSchedule(scheduleItem);\n        const input = {\n            tripReportScheduleID: scheduleItem.id,\n            departTime: scheduleItem.departTime,\n            arriveTime: scheduleItem.arriveTime,\n            fromLocationID: scheduleItem.fromLocationID,\n            toLocationID: scheduleItem.toLocationID,\n            logBookEntryID: logentryID\n        };\n        doCreateTripReport(input, true) // true indicates this is a scheduled trip\n        ;\n    };\n    // Implement confirmDeletetrip using  AlertNew component to confirm trip deletion\n    const confirmDeleteTrip = (item)=>{\n        if (item) {\n            setDeleteTripItem(item);\n            setDeleteTripConfirmation(true);\n        }\n    };\n    const [deleteTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.DeleteTripReport_LogBookEntrySections, {\n        onCompleted: ()=>{\n            // const data = response.deleteTripReport_LogBookEntrySection\n            // updateTripReport({\n            //     id: data,\n            //     key: 'archived',\n            //     value: true,\n            // })\n            updateTripReport({\n                id: [\n                    ...tripReport.filter((trip)=>trip.id !== deleteTripItem.id).map((trip)=>trip.id)\n                ]\n            });\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error deleting trip report\", error);\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        }\n    });\n    const handleDeleteTrip = async ()=>{\n        if (deleteTripItem) {\n            if (!offline) {\n                await deleteTripReport_LogBookEntrySection({\n                    variables: {\n                        ids: [\n                            deleteTripItem.id\n                        ]\n                    }\n                });\n            } else {\n                await tripReportModel.save({\n                    id: deleteTripItem.id,\n                    archived: true\n                });\n            }\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between gap-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__.Combobox, {\n                        options: tripScheduleServices,\n                        value: tripScheduleServices.find((option)=>option.value === selectedTripScheduleServiceID) || null,\n                        onChange: (e)=>{\n                            if (e) {\n                                setSelectedTripScheduleServiceID(e.value);\n                                loadTripReportSchedules(e.value);\n                            } else {\n                                setSelectedTripScheduleServiceID(null);\n                                setTripReportSchedules([]);\n                                setShowNextTrips(false);\n                            }\n                        },\n                        placeholder: \"Select Trip Schedule Service\",\n                        disabled: locked\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 1196,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 1195,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1194,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: \"This section covers the logbook entry. This can be made up of a single trip or many over the course of the voyage.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1249,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: combinedTripData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_22__.Accordion, {\n                    type: \"single\",\n                    collapsible: true,\n                    value: accordionValue,\n                    onValueChange: (value)=>{\n                        setAccordionValue(value);\n                        // If we're closing the accordion, reset the state\n                        if (value === \"\") {\n                            setSelectedTab(0);\n                            setOpenTripModal(false);\n                            setCurrentTrip(false);\n                            setSelectedTripReportSchedule(null);\n                        } else {\n                            // Find the selected item from combined data\n                            const selectedItem = combinedTripData.find((item)=>item.id.toString() === value);\n                            if (selectedItem) {\n                                if (selectedItem.isScheduled && !selectedItem.isCreated) {\n                                    // This is a scheduled trip that hasn't been created yet\n                                    // Don't set currentTrip, just expand the accordion\n                                    setSelectedTab(0);\n                                    setOpenTripModal(false);\n                                    setCurrentTrip(false);\n                                    setSelectedTripReportSchedule(null);\n                                } else {\n                                    // This is an existing trip or a created scheduled trip\n                                    setSelectedTab(selectedItem.id);\n                                    setOpenTripModal(true);\n                                    setSignatureKey(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default()());\n                                    currentTripRef.current = selectedItem;\n                                    setCurrentTrip(selectedItem);\n                                    setSelectedTripReportSchedule(null);\n                                    // Initialize signature data if available\n                                    if (selectedItem.sectionSignature) {\n                                        setSignature(selectedItem.sectionSignature.signatureData || \"\");\n                                    } else {\n                                        setSignature(\"\");\n                                    }\n                                    setRiskBufferEvDgr(selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.dangerousGoodsChecklist);\n                                    setOpenTripStartRiskAnalysis(false);\n                                    setAllDangerousGoods(false);\n                                    setCurrentStopEvent(false);\n                                    setCurrentEventTypeEvent(false);\n                                    setSelectedRowEvent(false);\n                                    setDisplayDangerousGoods((selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.enableDGR) === true);\n                                    setDisplayDangerousGoodsSailing((selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.designatedDangerousGoodsSailing) === true);\n                                    setDisplayDangerousGoodsPvpd(false);\n                                    setDisplayDangerousGoodsPvpdSailing(null);\n                                    setAllPVPDDangerousGoods(false);\n                                    setSelectedDGRPVPD(false);\n                                    setTripReport_Stops(false);\n                                }\n                            }\n                        }\n                    },\n                    children: combinedTripData.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_22__.AccordionItem, {\n                            value: item.id.toString(),\n                            id: \"combined-trip-\".concat(item.id),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_22__.AccordionTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.displayText\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1338,\n                                                        columnNumber: 45\n                                                    }, this),\n                                                    item.isScheduled && !item.isCreated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\",\n                                                        children: \"Scheduled\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1341,\n                                                        columnNumber: 53\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1337,\n                                                columnNumber: 41\n                                            }, this),\n                                            item.isScheduled && !item.isCreated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleCreateFromSchedule(item);\n                                                },\n                                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"],\n                                                size: \"sm\",\n                                                className: \"ml-2\",\n                                                disabled: locked,\n                                                children: \"Create Trip\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1348,\n                                                columnNumber: 49\n                                            }, this),\n                                            !item.isScheduled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    confirmDeleteTrip(item);\n                                                },\n                                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_43__[\"default\"],\n                                                size: \"sm\",\n                                                className: \"ml-2\",\n                                                variant: \"destructive\",\n                                                disabled: locked,\n                                                children: \"Delete Trip\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1363,\n                                                columnNumber: 45\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 1336,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                    lineNumber: 1335,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_22__.AccordionContent, {\n                                    className: \"px-5 sm:px-10\",\n                                    children: !item.isScheduled || item.isCreated ? currentTrip && currentTrip.id === item.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TripReportAccordionContent, {\n                                        currentTrip: currentTrip,\n                                        offline: offline,\n                                        tripReport: tripReport,\n                                        updateTripReport: updateTripReport,\n                                        updateTripReport_LogBookEntrySection: updateTripReport_LogBookEntrySection,\n                                        currentTripRef: currentTripRef,\n                                        locations: locations,\n                                        locked: locked,\n                                        edit_tripReport: edit_tripReport,\n                                        vessel: vessel,\n                                        crewMembers: crewMembers,\n                                        logBookConfig: logBookConfig,\n                                        client: client,\n                                        canCarryVehicles: canCarryVehicles,\n                                        canCarryDangerousGoods: canCarryDangerousGoods,\n                                        selectedDGR: selectedDGR,\n                                        displayDangerousGoods: displayDangerousGoods,\n                                        setDisplayDangerousGoods: setDisplayDangerousGoods,\n                                        displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                                        setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                                        allDangerousGoods: allDangerousGoods,\n                                        setAllDangerousGoods: setAllDangerousGoods,\n                                        logBookStartDate: logBookStartDate,\n                                        masterID: masterID,\n                                        vessels: vessels,\n                                        setSelectedRowEvent: setSelectedRowEvent,\n                                        setCurrentEventTypeEvent: setCurrentEventTypeEvent,\n                                        setCurrentStopEvent: setCurrentStopEvent,\n                                        currentEventTypeEvent: currentEventTypeEvent,\n                                        currentStopEvent: currentStopEvent,\n                                        tripReport_Stops: tripReport_Stops,\n                                        setTripReport_Stops: setTripReport_Stops,\n                                        displayDangerousGoodsPvpd: displayDangerousGoodsPvpd,\n                                        setDisplayDangerousGoodsPvpd: setDisplayDangerousGoodsPvpd,\n                                        displayDangerousGoodsPvpdSailing: displayDangerousGoodsPvpdSailing,\n                                        setDisplayDangerousGoodsPvpdSailing: setDisplayDangerousGoodsPvpdSailing,\n                                        allPVPDDangerousGoods: allPVPDDangerousGoods,\n                                        setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                                        selectedDGRPVPD: selectedDGRPVPD,\n                                        setSelectedDGRPVPD: setSelectedDGRPVPD,\n                                        fuelLogs: fuelLogs,\n                                        comment: comment,\n                                        setComment: setComment,\n                                        displayFieldTripLog: displayFieldTripLog,\n                                        signatureKey: signatureKey,\n                                        signature: signature,\n                                        setSignature: setSignature,\n                                        handleCancel: handleCancel,\n                                        handleSave: handleSave\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 1383,\n                                        columnNumber: 45\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-muted-foreground\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: 'Click the \"Create Trip\" button above to create this scheduled trip and access the trip details.'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 1493,\n                                            columnNumber: 45\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 1492,\n                                        columnNumber: 41\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                    lineNumber: 1378,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, \"combined-trip-\".concat(item.id, \"-\").concat(item.isScheduled ? \"scheduled\" : \"existing\"), true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1331,\n                            columnNumber: 29\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 1256,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1254,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_23__.AlertDialogNew, {\n                openDialog: deleteTripConfirmation,\n                setOpenDialog: setDeleteTripConfirmation,\n                // handleCreate={handleDeleteTrip}\n                title: \"Delete Trip\",\n                description: \"Are you sure you want to delete this trip? This action cannot be undone.\",\n                cancelText: \"Cancel\",\n                destructiveActionText: \"Delete\",\n                handleDestructiveAction: handleDeleteTrip,\n                showDestructiveAction: true,\n                variant: \"danger\",\n                actionText: \"Delete\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1508,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(TripLog, \"nlN3gY5V9iZg3D5RIXsBR/+0MBA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__.useToast,\n        nuqs__WEBPACK_IMPORTED_MODULE_39__.useQueryState,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useMutation\n    ];\n});\n_c1 = TripLog;\nvar _c, _c1;\n$RefreshReg$(_c, \"TripReportAccordionContent\");\n$RefreshReg$(_c1, \"TripLog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx\n"));

/***/ })

});